import { useFetchWorkers } from "../../../../../utils/costumeHook";

export default function LoadMore({ search }) {
  const { loadMore, loading } = useFetchWorkers();

  return (
    <button
      onClick={() => loadMore(search)}
      disabled={loading}
      className={`
        px-4 py-2 rounded-md justify-self-center
        ${
          loading
            ? "bg-gray-200 text-gray-500 cursor-not-allowed"
            : "bg-blue-600 text-white hover:bg-blue-700 cursor-pointer"
        }
        transition-colors duration-200 text-sm font-medium
      `}
    >
      {loading ? "Loading..." : "Load More"}
    </button>
  );
}
