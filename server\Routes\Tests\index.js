"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../../middlewares/auth");
const forceQuite_1 = require("../../middlewares/forceQuite");
const mongoose_1 = require("mongoose");
const Treatment_1 = require("../../Models/Treatment");
const Test_1 = require("../../Models/Test");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const multer = require("multer");
const fs = __importStar(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const router = (0, express_1.Router)();
router.use(auth_1.authMiddleware);
const upload = multer({ storage: multer.memoryStorage() });
// GET tests for a treatment
router.get("/:treatmentId", (0, forceQuite_1.forceQuitMiddleware)({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
}), async (req, res) => {
    const { treatmentId } = req.params;
    if (!mongoose_1.Types.ObjectId.isValid(treatmentId)) {
        res.status(400).json({ error: "Invalid treatment ID" });
        return;
    }
    const treatment = await Treatment_1.Treatment.findById(treatmentId).populate("tests").select({ tests: 1, _id: 0 }).lean();
    if (!treatment) {
        res.status(404).json({ error: "Treatment not found" });
        return;
    }
    res.json(treatment.tests || []);
});
// POST create a test and attach to treatment
router.post("/:treatmentId", (0, forceQuite_1.forceQuitMiddleware)({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
}), async (req, res) => {
    const { treatmentId } = req.params;
    const { title, description = "", files = [], state = "S" } = req.body;
    if (!mongoose_1.Types.ObjectId.isValid(treatmentId)) {
        res.status(400).json({ error: "Invalid treatment ID" });
        return;
    }
    if (!title) {
        res.status(400).json({ error: "title is required" });
        return;
    }
    const treatment = await Treatment_1.Treatment.findById(treatmentId);
    if (!treatment) {
        res.status(404).json({ error: "Treatment not found" });
        return;
    }
    const test = new Test_1.Test({ title, description, files, state, createdAt: Date.now(), updatedAt: Date.now() });
    await test.save();
    treatment.tests.push(test._id);
    await treatment.save();
    res.status(201).json(test);
});
// PUT update a test
router.put("/update/:testId", (0, forceQuite_1.forceQuitMiddleware)({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
}), async (req, res) => {
    const { testId } = req.params;
    if (!mongoose_1.Types.ObjectId.isValid(testId)) {
        res.status(400).json({ error: "Invalid test ID" });
        return;
    }
    const update = { updatedAt: Date.now() };
    const { title, description, files, state } = req.body;
    if (title !== undefined)
        update.title = title;
    if (description !== undefined)
        update.description = description;
    if (files !== undefined)
        update.files = files;
    if (state !== undefined)
        update.state = state;
    const test = await Test_1.Test.findByIdAndUpdate(testId, update, { new: true }).lean();
    if (!test) {
        res.status(404).json({ error: "Test not found" });
        return;
    }
    res.json(test);
});
// POST upload images for a completed test
router.post("/upload/:testId", (0, forceQuite_1.forceQuitMiddleware)({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
}), upload.array("images", 20), async (req, res) => {
    try {
        const { testId } = req.params;
        if (!mongoose_1.Types.ObjectId.isValid(testId)) {
            res.status(400).json({ error: "Invalid test ID" });
            return;
        }
        const test = await Test_1.Test.findById(testId);
        if (!test) {
            res.status(404).json({ error: "Test not found" });
            return;
        }
        if (test.state !== "C") {
            res.status(400).json({ error: "Images can only be uploaded for completed tests" });
            return;
        }
        // Find treatment to get patient id and treatment id
        const treatment = await Treatment_1.Treatment.findOne({ tests: test._id }).lean();
        if (!treatment) {
            res.status(404).json({ error: "Parent treatment not found" });
            return;
        }
        const patientId = treatment.patient.toString();
        const treatmentId = treatment._id.toString();
        const files = req.files || [];
        if (!files.length) {
            res.status(400).json({ error: "No images provided" });
            return;
        }
        const uploadsRoot = path_1.default.join(__dirname, "..", "..", "uploads");
        const testsDir = path_1.default.join(uploadsRoot, "tests");
        await fs.mkdir(testsDir, { recursive: true });
        // Starting index from existing files length
        let startIndex = Array.isArray(test.files) ? test.files.length : 0;
        const savedPaths = [];
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const now = Date.now();
            const imgNumber = startIndex + i; // 0-based index as requested
            const ext = (file.originalname && ("." + (file.originalname.split(".").pop() || ""))) || "";
            const name = `${now}_${patientId}_${treatmentId}_${imgNumber}${ext}`;
            const absPath = path_1.default.join(testsDir, name);
            await fs.writeFile(absPath, file.buffer);
            const publicPath = `/uploads/tests/${name}`;
            savedPaths.push(publicPath);
            test.files.push(publicPath);
        }
        test.updatedAt = Date.now();
        await test.save();
        res.status(201).json({ files: savedPaths, test });
    }
    catch (e) {
        console.error("Upload test images error:", e);
        res.status(500).json({ error: "Failed to upload images" });
    }
});
exports.default = router;
