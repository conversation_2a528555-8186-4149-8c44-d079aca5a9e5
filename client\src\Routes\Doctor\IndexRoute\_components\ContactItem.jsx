import { Box, IconButton } from "@mui/material";
import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";

export default function ContactItem({ phone, email }) {
  return (
    <Box className="flex gap-1">
      <IconButton
        color="primary"
        onClick={() => window.open(`tel:${phone}`, "_blank")}
      >
        <PhoneIcon />
      </IconButton>
      <IconButton
        color="primary"
        onClick={() => window.open(`mailto:${email}`, "_blank")}
      >
        <EmailIcon />
      </IconButton>
    </Box>
  );
}
