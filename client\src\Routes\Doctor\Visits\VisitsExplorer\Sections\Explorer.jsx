import { InputAdornment, TextField } from "@mui/material";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  visitsExplorerStore,
} from "../../../../../data";
import { useEffect, useState } from "react";

import LoadMore from "../_components/LoadMore";
import Render from "../_components/Rendrer";
import ContainerPopUp from "../../../../../utils/ContainerPopUp";
import VisitAddForm from "../_components/VisitAddForm";
import TreatmentAddForm from "../_components/TreatementAddForm";
import PatientAddForm from "../_components/PatientAddForm";
import {
  useDebounce,
  useFetchPatients,
} from "../../../../../utils/costumeHook";
import PatientsAdvancedSearch from "../_components/PatientsAdvancedSearch";
import { searchPatientViaAdvancedOptionsAPI } from "../../../../../api";
import toast from "react-hot-toast";
import { ResizableDiv } from "../../../../../utils/ResizableDiv";

export default function Explorer({}) {
  const visitsExplorerState = visitsExplorerStore.useStore({ setter: false });
  const [search, setSearch] = useState("");
  const { loadMore } = useFetchPatients();
  const debouncer = useDebounce(300);
  async function AdvancedPatentSearch(
    e = {
      cin: "",
      name: "",
      phone: "",
      email: "",
      birthDate: Date.now(),
      searchType: "exact", // 'exact' or 'partial'
    }
  ) {
    LoadingBarStore.setCurrent({ loading: true });
    const [err, data] = await searchPatientViaAdvancedOptionsAPI(
      localStorage.getItem("token"),
      {
        ...e,
        exact: e.searchType == "exact" ? "true" : undefined,
      }
    );
    if (err) {
      console.log("err:", err);
      toast.error(err?.response?.data?.error || "error searching for patient");
      LoadingBarStore.setCurrent({ loading: false });
      return;
    }
    const patients = PatientsDataStore.getCurrent().patient;
    const skip = [];
    patients.forEach((patient) => {
      const index = data.findIndex((data) => data._id == patient._id);
      if (index >= 0) {
        skip.push(index);
      }
      return;
    });
    data.forEach((data, i) => {
      if (skip.includes(i)) return;
      patients.push(data);
    });

    PatientsDataStore.setCurrent({ ...PatientsDataStore.getCurrent() });
    LoadingBarStore.setCurrent({ loading: false });
    PopUpElement.setCurrent({ element: () => null });
    if (e.cin) {
      setSearch(e.cin);
    }
  }

  return (
    <ResizableDiv seperator={{
      width: 2,
      style: "solid",
      color: "#1447e6",
      error_margin:20
    }}  className="w-[30%] min-w-[120px] border-r border-gray-200 h-full p-3 bg-gray-50 flex flex-col justify-center">
      <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center truncate">
        <i className="fa-solid fa-folder mr-2 text-blue-500"></i>
        {typeof visitsExplorerState.visit === "number" ||
        typeof visitsExplorerState.treatement === "number"
          ? "Visits"
          : typeof visitsExplorerState.patient === "number"
          ? "Treatments"
          : "Patients"}
      </h3>

      <div className="flex gap-2">
        <TextField
          variant="outlined"
          size="small"
          placeholder="Search..."
          fullWidth
          value={search}
          onChange={(e) => {
            setSearch(e.target.value);
            const search = e.target.value;
            if (search.includes("@")) {
              //enable advanced search
              PopUpElement.setCurrent({
                element: () => (
                  <ContainerPopUp>
                    <PatientsAdvancedSearch onSearch={AdvancedPatentSearch} />
                  </ContainerPopUp>
                ),
              });
              setSearch("");
              return;
            }
            debouncer(() => {
              loadMore(search);
            });
          }}
          className="mb-3"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <i className="fa-solid fa-magnifying-glass text-gray-400"></i>
              </InputAdornment>
            ),
            className: "bg-white lack",
          }}
        />
        <button
          className="bg-blue-50 border-blue-100 border-1 rounded-[5px] text-blue-700 w-9"
          onClick={() => {
            if (typeof visitsExplorerState.treatement == "number") {
              PopUpElement.setCurrent({
                element: () => (
                  <ContainerPopUp>
                    <VisitAddForm />
                  </ContainerPopUp>
                ),
              });
              return;
            }
            if (typeof visitsExplorerState.patient == "number") {
              PopUpElement.setCurrent({
                element: () => (
                  <ContainerPopUp>
                    <TreatmentAddForm />
                  </ContainerPopUp>
                ),
              });
              return;
            }
            PopUpElement.setCurrent({
              element: () => (
                <ContainerPopUp>
                  <PatientAddForm />
                </ContainerPopUp>
              ),
            });
          }}
        >
          +
        </button>
      </div>
      <ul className="flex-1 overflow-y-auto space-y-1 mt-3">
        <Render search={search} setSearch={setSearch} />
        {typeof visitsExplorerState.patient != "number" ? (
          <LoadMore search={search} />
        ) : null}
      </ul>
    </ResizableDiv>
  );
}
