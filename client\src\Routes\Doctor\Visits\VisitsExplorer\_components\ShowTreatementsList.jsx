export default function ShowTreatementsList({
  treatements = [],
  onSelect = () => {},
  searchInput = "",
}) {
  return (
    <>
      {treatements.map((treatement, i) => {
        if (!treatement.title.toLowerCase().includes(searchInput.toLowerCase()))
          return null;
        return (
          <li
            key={treatement._id}
            onClick={() => onSelect(i, treatement._id)}
            className="px-3 py-2 cursor-pointer hover:bg-accent/10 rounded-md transition-colors
                    text-secondary hover:text-primary border border-transparent hover:border-accent/50"
          >
            <div className="flex items-center min-w-0">
              <i className="fa-solid fa-file-medical mr-2 text-primary flex-shrink-0"></i>
              <span className="truncate">{treatement.title}</span>
            </div>
          </li>
        );
      })}
    </>
  );
}
