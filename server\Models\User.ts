import { Schema, model, Types } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";
import { roles, rolesType } from "../utils/roles";

export interface IUser extends BaseModel {
  name: string[]; // Full name split into parts
  email: string;
  phone: string | null;
  img: string | null;
  gender: "F" | "M";
  BirthDay: number; // Unix timestamp
  password: string;
  cin: string; // Assuming CIN is the ID required
  is_deleted?: boolean; // Soft delete flag
  profiles: {
    type: rolesType; // e.g., "doctor", "patient"
    hospital: Types.ObjectId; // Reference to the facility
    specialization?: string; // Optional specialization for doctors
    createdAt: number; // Unix timestamp
    updatedAt: number; // Unix timestamp
  }[];
}
const profileSchema = new Schema(
  {
    type: {
      type: String,
      enum: Object.values(roles),
      required: true,
      default: "patient", // Default type is patient
    },
    hospital: {
      type: Schema.Types.ObjectId,
      ref: ModelRefs.Facility.modelName,
      // hospital is only for doctor and admin profiles
      default: null,
    },
    specialization: {
      type: String,
      default: "",
      required: false,
      // specialization only relevant for doctor
    },
    createdAt: { type: Number, required: true },
    updatedAt: { type: Number, required: true },
    
  },
  { _id: false }
);

// Add a custom validator to conditionally require fields based on type:
profileSchema.path("hospital").validate(function (value) {
  if (this.type === "doctor" || this.type === "admin") {
    return value != null;
  }
  return true; // for other types, hospital can be null
}, "Hospital is required for doctor and admin profiles");

/*profileSchema.path("specialization").validate(function (value) {
  if (this.type === "doctor") {
    return value && value.length > 0;
  }
  return true;
}, "Specialization is required for doctor profiles");
*/
const userSchema = new Schema<IUser>({
  name: [{ type: String, required: true }],
  profiles: {type:[profileSchema],required:true}, // use the sub-schema here
  email: { type: String, required: true, unique: true, index: true },
  phone: { type: String, default: null },
  img: { type: String, default: null },
  gender: { type: String, enum: ["F", "M"], required: true },
  BirthDay: { type: Number, required: true },
  password: { type: String, required: true },
  cin: { type: String, required: true, unique: true, index: true },
  is_deleted: { type: Boolean, default: false },
  createdAt: Number,
  updatedAt: Number,
});

// Indexes to speed up facility-scoped queries
userSchema.index({ "profiles.hospital": 1, "profiles.type": 1, is_deleted: 1 });


userSchema.virtual("folder", {
  ref: ModelRefs.Folder.modelName,
  localField: "_id",
  foreignField: "patient",
  justOne: true,
});
userSchema.set("toObject", { virtuals: true,  transform(doc, ret) {
    delete ret.password;
    return ret;
  } });
userSchema.set("toJSON", { virtuals: true,   transform(doc, ret) {
    delete ret.password;
    return ret;
  } });



export const User = model<IUser>(ModelRefs.User.modelName, userSchema);
