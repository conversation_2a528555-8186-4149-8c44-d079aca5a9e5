import { PopUpElement } from "../data";

const ContainerPopUp = ({ children }) => {
  const setPopUp = PopUpElement.useStore({ getter: false });
  return (
    <div
      className="fixed  top-0 left-0 w-[100vw] h-[100vh]  bg-[#00000033] backdrop-blur-[2px]  flex justify-center items-center popup-container"
      style={{
        zIndex: 1020,
      }}
      onClick={(e) => {
        if (e.target !== e.currentTarget) return;
        setPopUp({ element: () => null });
      }}
    >
      {children}
    </div>
  );
};

export default ContainerPopUp;
