import { useState } from "react";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import CheckCircleOutline from "@mui/icons-material/CheckCircleOutline";
import { PopUpElement } from "../../../data";

const ProfileSelector = ({ profiles, onSubmit }) => {
  const [selectedIndex, setSelectedIndex] = useState(null);

  const handleProfileSelect = (index) => {
    setSelectedIndex(index);
  };

  const handleSubmit = () => {
    if (selectedIndex !== null) {
      onSubmit(selectedIndex);
    }
  };

  // Map profile types to Font Awesome icons
  const getProfileIcon = (type) => {
    switch (type) {
      case "admin":
        return <i className="fas fa-user-shield text-white text-2xl"></i>;
      case "sudo":
        return <i className="fas fa-user-cog text-white text-2xl"></i>;
      case "doctor":
        return <i className="fas fa-user-md text-white text-2xl"></i>;
      case "patient":
        return <i className="fas fa-user-injured text-white text-2xl"></i>;
      case "facility-manager":
        return <i className="fas fa-clipboard-list text-white text-2xl"></i>;
      default:
        return <i className="fas fa-user text-white text-2xl"></i>;
    }
  };

  // Map profile types to background colors
  const getProfileColor = (type) => {
    switch (type) {
      case "admin":
        return "bg-purple-600";
      case "sudo":
        return "bg-red-600";
      case "doctor":
        return "bg-blue-600";
      case "patient":
        return "bg-green-600";
      case "facility-manager":
        return "bg-orange-600";
      default:
        return "bg-gray-600";
    }
  };

  return (
    <div className="w-full max-w-md mx-auto p-4 bg-white">
      <Typography
        variant="h5"
        className="text-center mb-6 font-semibold text-gray-800"
      >
        Select a Profile to Log In
      </Typography>

      <div className="space-y-4 mb-8">
        {profiles.map((profile, index) => (
          <Card
            key={profile.type}
            onClick={() => handleProfileSelect(index)}
            className={`cursor-pointer transition-all ${
              selectedIndex === index
                ? "ring-2 ring-blue-500 bg-blue-50"
                : "hover:bg-gray-50"
            }`}
            variant="outlined"
          >
            <CardContent className="flex items-center p-4 gap-4">
              <Avatar
                className={`${getProfileColor(
                  profile.type
                )} w-12 h-12 flex items-center justify-center`}
              >
                {getProfileIcon(profile.type)}
              </Avatar>

              <div className="flex-1">
                <Typography
                  variant="subtitle1"
                  className="font-medium capitalize"
                >
                  {profile.type.replace("-", " ")}
                </Typography>
                {profile.hospitalName && (
                  <Typography variant="body2" className="text-gray-600">
                    {profile.hospitalName}
                    {profile.hospitalType && (
                      <span className="text-gray-500 ml-1">
                        ({profile.hospitalType})
                      </span>
                    )}
                  </Typography>
                )}
                {profile.specialization && profile.type === "doctor" && (
                  <Typography variant="caption" className="text-gray-500">
                    {profile.specialization}
                  </Typography>
                )}
              </div>

              {selectedIndex === index && (
                <CheckCircleOutline className="text-blue-500 ml-2" />
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <Button
        variant="contained"
        color="primary"
        fullWidth
        size="large"
        onClick={() => {
          handleSubmit();
          PopUpElement.setCurrent({ element: () => null });
        }}
        disabled={selectedIndex === null}
        className="py-2"
      >
        Continue
      </Button>
    </div>
  );
};

export default ProfileSelector;
