import { <PERSON><PERSON>, TextField } from "@mui/material";
import ContainerPopUp from "../../../../utils/ContainerPopUp";
import { LocalizationProvider, TimeClock } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { currentAppointement, PopUpElement } from "../../../../data";
import { VisitPopUp } from "../../../../utils/Components";

export default function ({ appointement }) {
  if (appointement.patient)
    return <PatientHasProfile appointement={appointement} />;
  return <PatientDontHaveProfile appointement={appointement} />;
}

function PatientHasProfile() {
  return <>have profile</>;
}
function PatientDontHaveProfile({ appointement }) {
  const setPopUpPage = PopUpElement.useStore({ getter: false });
  if (!appointement) return;
  const localDate = new Date(appointement.date);
  localDate.setHours(localDate.getHours() - localDate.getTimezoneOffset() / 60);
  console.log(appointement);
  return (
    <ContainerPopUp>
      <div className="w-[76%] h-[76%] bg-white rounded-md shadow-md flex overflow-hidden m-auto text-black">
        <div className="w-1/2 p-5 border-r border-gray-200 box-border">
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <TimeClock
              value={new Date(appointement.date)}
              view="hours"
              views={["hours", "minutes"]}
            />
          </LocalizationProvider>
          <TextField
            type="datetime-local"
            disabled
            className="w-full mt-2.5"
            value={localDate.toISOString().split(".")[0]}
          />
        </div>
        <div className="flex flex-col gap-4 w-1/2 p-5 box-border overflow-y-auto">
          <TextField
            label="title"
            placeholder="Title"
            variant="outlined"
            value={appointement.title}
            fullWidth
          />
          <TextField
            label="description"
            placeholder="Description"
            variant="outlined"
            fullWidth
            value={appointement.description}
            multiline
            rows={3}
          />
          <TextField
            label="patient CIN"
            placeholder="patient  CIN (optional)"
            variant="outlined"
            fullWidth
            value={appointement.patientCIN}
          />
          <TextField
            label="additionalInfo"
            placeholder="Additional Info"
            variant="outlined"
            fullWidth
            value={appointement.additionalInfo}
          />
          <TextField
            label="notes"
            placeholder="Notes"
            variant="outlined"
            fullWidth
            value={appointement.notes}
            multiline
            rows={3}
          />
          <Button
            variant="outlined"
            onClick={() => {
              //! check the data.js export it's temporarly untill using the gobal stores;
              currentAppointement.cin = appointement.patientCIN;
              setPopUpPage({ element: () => <VisitPopUp /> });
            }}
          >
            visit
          </Button>
        </div>
      </div>
    </ContainerPopUp>
  );
}
