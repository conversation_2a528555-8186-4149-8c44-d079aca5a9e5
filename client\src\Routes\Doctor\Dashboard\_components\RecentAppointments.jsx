import { Avatar } from "@mui/material";
import { CalendarToday as CalendarIcon } from "@mui/icons-material";

export default function RecentAppointments({ appointments }) {
  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (date) => {
    const now = Date.now();
    const appointmentDate = new Date(date).getTime();

    if (appointmentDate < now) {
      return "bg-gray-100 text-gray-800"; // Past
    } else if (appointmentDate - now < 24 * 60 * 60 * 1000) {
      return "bg-yellow-100 text-yellow-800"; // Today/Tomorrow
    } else {
      return "bg-blue-100 text-blue-800"; // Future
    }
  };

  const getStatusText = (date) => {
    const now = Date.now();
    const appointmentDate = new Date(date).getTime();

    if (appointmentDate < now) {
      return "Completed";
    } else if (appointmentDate - now < 24 * 60 * 60 * 1000) {
      return "Today";
    } else {
      return "Upcoming";
    }
  };

  if (!appointments || appointments.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">
        No recent appointments
      </div>
    );
  }

  return (
    <div className="max-h-80 overflow-y-auto divide-y divide-gray-100">
      {appointments.map((appointment, index) => (
        <div
          key={appointment._id || index}
          className="flex items-start py-4 px-2 hover:bg-gray-50"
        >
          <div className="mr-3">
            <Avatar className="bg-blue-100 text-blue-600">
              <CalendarIcon />
            </Avatar>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium truncate">
                {appointment.title || "Appointment"}
              </h4>
              <span
                className={`text-xs px-2 py-1 rounded-full ${getStatusColor(
                  appointment.date
                )}`}
              >
                {getStatusText(appointment.date)}
              </span>
            </div>
            <div className="text-xs text-gray-500 space-y-0.5 mt-1">
              <p className="truncate">
                Patient: {appointment.patient?.name?.join(" ") || "Unknown"}
              </p>
              <p className="text-xs text-gray-400">
                {formatDate(appointment.date)}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
