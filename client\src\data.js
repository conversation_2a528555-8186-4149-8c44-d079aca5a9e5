import { createStore } from "react-data-stores";
export const roles = {
  admin: "admin",
  sudo: "sudo",
  doctor: "doctor",
  patient: "patient",
  facilityManager: "facility-manager",
  facilityManager: "facility-manager",
};

export const userDataStore = createStore({
  token: null,
  data: { role: roles.doctor },
});
export const LoadingBarStore = createStore({ loading: false });
export const NotBackLinkTo = ["/login"];
export const PopUpElement = createStore({
  element: () => null,
});
export const HomePageFor = {
  [roles.doctor]: "/doctor",
  [roles.facilityManager]: "/facility",
  [roles.facilityManager]: "/facility",
};
export const UserToken = "token";
export const NotificationElements = createStore({ elements: [] });
export const appointementStore = createStore({ appointement: [] });
export const currentAppointement = {};
export const PatientsDataStore = createStore({
  patient: [],
});
export const WorkersDataStore = createStore({
  worker: [],
});
export const FLAGS = { loadeMore: { patients: [], workers: [] } };
export const visitsExplorerStore = createStore({
  patient: undefined,
  treatement: undefined,
  visit: undefined,
});
export const workersExplorerStore = createStore({
  worker: undefined,
});
