import { WorkersDataStore, workersExplorerStore } from "../../../../../data";

import ShowWorkerData from "../_components/ShowWorkerData";

export default function Content({}) {
  const render = workersExplorerStore.useStore({ setter: false });
  const workersData = WorkersDataStore.useStore({ setter: false });

  // Default render when nothing is selected
  const rtHtml = (
    <div className="w-[70%] h-full p-5 flex items-center justify-center bg-gray-50 border-gray-200">
      <p className="text-gray-700 text-lg">Select a worker to see details.</p>
    </div>
  );

  // If no worker is selected, return default
  if (
    typeof render.worker !== "number" ||
    !workersData.worker[render.worker]
  ) {
    return rtHtml;
  }

  // Get worker data
  const worker = workersData.worker[render.worker];

  return (
    <div className="w-[70%] h-full p-6 overflow-y-auto bg-white lack border-gray-200 shadow-sm">
      <div className="space-y-6">
        <ShowWorkerData worker={worker} />
      </div>
    </div>
  );
}
