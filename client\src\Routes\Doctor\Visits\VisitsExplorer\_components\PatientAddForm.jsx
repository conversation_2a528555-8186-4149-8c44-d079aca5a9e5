import {
  Box,
  Button,
  TextField,
  Typography,
  MenuItem,
  Select,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useState } from "react";
import { createPatientAPI } from "../../../../../api";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import toast from "react-hot-toast";

export default function PatientAddForm({}) {
  const [formData, setFormData] = useState({
    cin: "",
    firstName: "",
    lastName: "",
    birthDate: null,
    gender: "",
    email: "",
    phone: "",
  });
  const [patients, setPatients] = PatientsDataStore.useStore();
  const setCurrentSelectionState = visitsExplorerStore.useStore({
    getter: false,
  });
  const userData = userDataStore.useStore({ setter: false });

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      birthDate: date,
    });
  };

  const handleSubmit = async (e) => {
    LoadingBarStore.setCurrent({ loading: true });
    e.preventDefault();
    formData.name = [formData.firstName, formData.lastName];
    formData.birthDate = formData.birthDate ? formData.birthDate.getTime() : null;
    const [err, data] = await createPatientAPI(formData, userData.token);
    if (err) {
      toast.error("error adding patient");
      console.log(err);
      LoadingBarStore.setCurrent({ loading: false });
      return;
    }
    toast.success("patient added succesfully");
    PopUpElement.setCurrent({ element: () => null });
    const newLength = patients.patient.push(data.data);
    setPatients({ ...patients });
    setCurrentSelectionState({
      patient: newLength - 1,
      treatement: undefined,
      visit: undefined,
    });
    LoadingBarStore.setCurrent({ loading: false });
    console.log(data);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="!mb-4">
        New Patient
      </Typography>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          className="min-w-[180px] flex-1"
          label="CIN"
          required
          value={formData.cin}
          onChange={(e) => setFormData({ ...formData, cin: e.target.value })}
          variant="outlined"
        />
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          className="min-w-[180px] flex-1"
          label="First Name"
          required
          value={formData.firstName}
          onChange={(e) =>
            setFormData({ ...formData, firstName: e.target.value })
          }
          variant="outlined"
        />
        <TextField
          className="min-w-[180px] flex-1"
          label="Last Name"
          required
          value={formData.lastName}
          onChange={(e) =>
            setFormData({ ...formData, lastName: e.target.value })
          }
          variant="outlined"
        />
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Birth Date"
            value={formData.birthDate}
            onChange={handleDateChange}
            className="min-w-[180px] flex-1"
            renderInput={(params) => (
              <TextField
                {...params}
                required
                variant="outlined"
              />
            )}
          />
        </LocalizationProvider>
        <Select
          className="min-w-[180px] flex-1"
          label="Gender"
          required
          value={formData.gender}
          onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
          variant="outlined"
        >
          <MenuItem value="M">Male</MenuItem>
          <MenuItem value="F">Female</MenuItem>
        </Select>
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          className="min-w-[180px] flex-1"
          label="Email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          variant="outlined"
        />
        <TextField
          className="min-w-[180px] flex-1"
          label="Phone Number"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          variant="outlined"
        />
      </Box>

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Save Patient
        </Button>
      </div>
    </Box>
  );
}
