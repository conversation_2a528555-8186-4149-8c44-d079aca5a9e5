import React from "react";
import AnalogClock from "analog-clock-react";

export default function ClockWithoutSeconds() {
  const options = {
    width: "200px",
    border: true,
    borderColor: "#1976d2",
    baseColor: "#fff",
    centerColor: "#1565c0",
    centerBorderColor: "#0d47a1",
    handColors: {
      second: "#fff",
      minute: "#1976d2",
      hour: "#0d47a1",
    },
  };

  return <div style={{ position: "relative", width: options.width }}></div>;
}
