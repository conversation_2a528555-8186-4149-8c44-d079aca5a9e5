import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { getDoctorProfileAPI, updateDoctorProfileAPI } from "../../../api";
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { LoadingBarStore } from "../../../data";
import { formatDate } from "../../../utils/dateFormater";

export default function DoctorProfile() {
  const [profile, setProfile] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = LoadingBarStore.useStore();
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: [],
    email: "",
    phone: "",
    specialization: "",
    img: "",
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading({ loading: true });
      const token = localStorage.getItem("token");
      const [err, data] = await getDoctorProfileAPI(token);

      if (err) {
        toast.error("Failed to load profile");
        return;
      }

      setProfile(data.data);
      setFormData({
        name: data.data.name || [],
        email: data.data.email || "",
        phone: data.data.phone || "",
        specialization: data.data.specialization || "",
        img: data.data.img || "",
      });
    } catch (err) {
      console.error("Profile error:", err);
      toast.error("Failed to load profile");
    } finally {
      setLoading({ loading: false });
    }
  };

  const handleInputChange = (field, value) => {
    if (field === "name") {
      setFormData((prev) => ({
        ...prev,
        name: value.split(" ").filter((n) => n.trim()),
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setLoading({ loading: true });
      const token = localStorage.getItem("token");

      const [err, data] = await updateDoctorProfileAPI(token, formData);

      if (err) {
        toast.error("Failed to update profile");
        setLoading({ loading: false });
        return;
      }

      setProfile(data.data);
      toast.success("Profile updated successfully");
      setLoading({ loading: false });
      setEditMode(false);
    } catch (err) {
      console.error("Update error:", err);
      toast.error("Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: profile.name || [],
      email: profile.email || "",
      phone: profile.phone || "",
      specialization: profile.specialization || "",
      img: profile.img || "",
    });
    setEditMode(false);
  };

  const getInitials = (name) => {
    if (!name || !Array.isArray(name)) return "D";
    return name.map((n) => n.charAt(0).toUpperCase()).join("");
  };

  if (loading.loading) {
    return null;
  }

  return (
    <div className="p-6 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Doctor Profile</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Summary Card */}
        <div className="lg:col-span-1">
          <div className="bg-white lack rounded-xl shadow-sm p-6 sticky top-6">
            <div className="flex flex-col items-center">
              <div className="relative mb-4">
                {profile?.img ? (
                  <img
                    src={profile.img}
                    alt={profile.name?.join(" ")}
                    className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-md"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-blue-300 flex items-center justify-center text-white text-4xl font-bold shadow-md">
                    {getInitials(profile?.name)}
                  </div>
                )}
              </div>

              <h2 className="text-xl font-semibold text-gray-800 mb-1">
                {profile?.name?.join(" ") || "Doctor"}
              </h2>

              <p className="text-blue-600 font-medium mb-6">
                {profile?.specialization || "General Practice"}
              </p>

              <div className="w-full border-t border-gray-100 pt-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Current Profile</span>
                  <span className="font-medium">
                    {profile?.role || "Doctor"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Gender</span>
                  <span className="font-medium">
                    {profile?.gender === "M" ? "Male" : "Female"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">CIN</span>
                  <span className="font-medium">{profile?.cin || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Hospital</span>
                  <span className="font-medium">
                    {profile?.hospital?.name || "Not assigned"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Member since</span>
                  <span className="font-medium">
                    {formatDate(profile?.createdAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Edit Profile Card */}
        <div className="lg:col-span-2">
          <div className="bg-white lack rounded-xl shadow-sm p-6">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-xl font-semibold text-gray-800">
                Personal Information
              </h2>
              {!editMode ? (
                <button
                  onClick={() => setEditMode(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <EditIcon className="mr-2" />
                  Edit Profile
                </button>
              ) : (
                <div className="flex gap-3">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-70"
                  >
                    <SaveIcon className="mr-2" />
                    {saving ? "Saving..." : "Save"}
                  </button>
                  <button
                    onClick={handleCancel}
                    disabled={saving}
                    className="flex items-center px-4 py-2 border border-gray-300 hover:bg-gray-50 text-gray-700 rounded-lg transition-colors disabled:opacity-70"
                  >
                    <CancelIcon className="mr-2" />
                    Cancel
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  value={formData.name.join(" ")}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  disabled={!editMode}
                  className={`w-full px-4 py-2 rounded-lg border ${
                    editMode
                      ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      : "border-transparent bg-gray-50"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  disabled={!editMode}
                  className={`w-full px-4 py-2 rounded-lg border ${
                    editMode
                      ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      : "border-transparent bg-gray-50"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  disabled={!editMode}
                  className={`w-full px-4 py-2 rounded-lg border ${
                    editMode
                      ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      : "border-transparent bg-gray-50"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Specialization
                </label>
                <input
                  type="text"
                  value={formData.specialization}
                  onChange={(e) =>
                    handleInputChange("specialization", e.target.value)
                  }
                  disabled={!editMode}
                  className={`w-full px-4 py-2 rounded-lg border ${
                    editMode
                      ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      : "border-transparent bg-gray-50"
                  }`}
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Image URL
                </label>
                <input
                  type="url"
                  value={formData.img}
                  onChange={(e) => handleInputChange("img", e.target.value)}
                  disabled={!editMode}
                  className={`w-full px-4 py-2 rounded-lg border ${
                    editMode
                      ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      : "border-transparent bg-gray-50"
                  }`}
                  placeholder="https://example.com/profile.jpg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
