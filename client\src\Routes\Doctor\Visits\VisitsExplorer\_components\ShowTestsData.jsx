import { useState } from "react";
import { LoadingBarStore, PopUpElement } from "../../../../../data";
import ContainerPopUp from "../../../../../utils/ContainerPopUp";
import TestAddForm from "./TestAddForm";
import { BACKEND_BASE_URL, uploadTestImagesAPI } from "../../../../../api";
import toast from "react-hot-toast";

function TestImages({ test, collapsedDefault = true, onUploaded = () => {} }) {
  const [collapsed, setCollapsed] = useState(collapsedDefault);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async (e) => {
    const files = Array.from(e.target.files || []);
    if (!files.length) return;
    setUploading(true);
    LoadingBarStore.setCurrent({ loading: true });
    try {
      const [err, data] = await uploadTestImagesAPI(
        test._id,
        files,
        localStorage.getItem("token")
      );
      if (err) {
        toast.error("Upload failed");
        return;
      }
      toast.success("Images uploaded");
      onUploaded(data.test);
    } catch (ex) {
      console.error(ex);
      toast.error("Upload failed");
    } finally {
      setUploading(false);
      LoadingBarStore.setCurrent({ loading: false });
      e.target.value = "";
    }
  };

  return (
    <div className="bg-white p-3 rounded-lg shadow-sm mb-3">
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium text-gray-800 flex items-center">
          <i className="fas fa-images text-blue-500 mr-2"></i>
          Test Images
        </h4>
        
        <div className="flex space-x-4">
          <button
            type="button"
            className="text-blue-600 hover:text-blue-800 font-medium flex items-center text-sm"
            onClick={() => setCollapsed((c) => !c)}
          >
            <i className={`fas fa-eye${collapsed ? '' : '-slash'} mr-2`}></i>
            {collapsed ? "Show" : "Hide"}
          </button>
          
          {test.state === "C" && (
            <label className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer flex items-center text-sm">
              <i className={`fas fa-upload mr-2 ${uploading ? 'opacity-50' : ''}`}></i>
              {uploading ? "Uploading..." : "Upload"}
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleUpload}
                className="hidden"
                disabled={uploading}
              />
            </label>
          )}
        </div>
      </div>

      {!collapsed && (
        <div className="bg-gray-50 p-3 rounded-lg">
          {(test.files && test.files.length > 0) ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {test.files.map((src, idx) => (
                <a 
                  key={idx} 
                  href={BACKEND_BASE_URL + src} 
                  target="_blank" 
                  rel="noreferrer"
                  className="group"
                >
                  <div className="border border-gray-200 rounded-lg overflow-hidden hover:border-blue-500 transition-colors">
                    <img
                      src={BACKEND_BASE_URL + src}
                      alt={`test-${idx}`}
                      className="w-full h-32 object-cover"
                      loading="lazy"
                    />
                    <div className="p-2 bg-gray-100 text-center text-xs text-gray-600 group-hover:text-blue-600">
                      Image {idx + 1}
                    </div>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 bg-gray-100 rounded-lg">
              <i className="fas fa-image text-gray-300 text-3xl mb-2"></i>
              <p className="text-gray-500 text-sm">No images available</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default function ShowTestsData({ tests = [], treatmentId, onTestsChange = () => {} }) {
  const handleAdd = () => {
    PopUpElement.setCurrent({
      element: () => (
        <ContainerPopUp>
          <TestAddForm treatmentId={treatmentId} />
        </ContainerPopUp>
      ),
    });
  };

  const handleTestUpdated = (updatedTest) => {
    onTestsChange(updatedTest);
  };

  return (
    <div className="pb-4">
      <h2 className="text-2xl font-bold text-gray-900 mb-3 flex items-center">
        <i className="fas fa-vials text-blue-600 mr-2"></i>
        Tests
      </h2>

      <div className="bg-gray-100 p-4 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-800 flex items-center">
            <i className="fas fa-list text-gray-500 mr-2"></i>
            Test Records
          </h3>
          <button
            className="text-blue-600 hover:text-blue-800 transition-colors flex items-center gap-1 text-sm"
            onClick={handleAdd}
          >
            <i className="fas fa-plus"></i>
            <span>Add Test</span>
          </button>
        </div>

        {(!tests || tests.length === 0) ? (
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <i className="fas fa-vial text-gray-300 text-3xl mb-2"></i>
            <p className="text-gray-500">No tests for this treatment.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {tests.map((t) => (
              <div key={t._id} className="bg-white p-3 rounded-lg shadow-sm">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <div className="font-medium text-gray-900">{t.title}</div>
                    {t.description && (
                      <div className="text-sm text-gray-600">{t.description}</div>
                    )}
                  </div>
                  <span className={`text-xs px-2 py-1 rounded ${
                    t.state === "C" 
                      ? "bg-green-100 text-green-800" 
                      : "bg-blue-100 text-blue-800"
                  }`}>
                    {t.state === "C" ? "Completed" : "Scheduled"}
                  </span>
                </div>
                <TestImages test={t} onUploaded={handleTestUpdated} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}