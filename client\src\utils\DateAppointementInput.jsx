import {
  DateTimePickerTabs,
  LocalizationProvider,
  TimeClock,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useEffect, useState } from "react";
import CalenderApointement from "./CalendarApointement";

export default function DateAppointementInput({
  onChange = (e = new Date()) => {},
  dateInput = new Date(),
}) {
  console.log(dateInput);
  const [date, setDate] = useState(dateInput);
  const [isTime, setIsTime] = useState(false);
  useEffect(() => {
    onChange(date);
  }, [date]);
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateTimePickerTabs
        view={isTime ? "hours" : "day"}
        onViewChange={(view) => {
          if (view == "hours" || view == "minutes" || view == "seconds") {
            return setIsTime(true);
          }
          setIsTime(false);
        }}
      />
      {!isTime ? (
        <CalenderApointement selectedDate={date} setSelectedDate={setDate} />
      ) : (
        <TimeClock
          onChange={(e) => {
            setDate(e);
          }}
          value={date}
          ampmInClock={true}
          showViewSwitcher={true}
          views={["hours", "minutes"]}
        />
      )}
    </LocalizationProvider>
  );
}
