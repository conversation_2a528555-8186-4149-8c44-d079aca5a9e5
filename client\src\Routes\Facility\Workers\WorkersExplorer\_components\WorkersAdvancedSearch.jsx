import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  IconButton,
  Paper,
  InputLabel,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";

export default function WorkersAdvancedSearch({ onSearch }) {
  const [formData, setFormData] = useState({
    cin: "",
    name: "",
    phone: "",
    email: "",
    role: "",
    specialization: "",
    searchType: "partial",
  });

  const handleChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch(formData);
  };

  const handleReset = () => {
    setFormData({
      cin: "",
      name: "",
      phone: "",
      email: "",
      role: "",
      specialization: "",
      searchType: "partial",
    });
  };

  return (
    <Paper elevation={3} className="p-4 w-[70%]">
      <Box component="form" onSubmit={handleSubmit}>
        <Typography variant="h6" component="h3" className="!mb-4">
          Advanced Worker Search
        </Typography>

        <Grid container spacing={2}>
          {/* Row 1 */}
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="CIN"
              value={formData.cin}
              onChange={handleChange("cin")}
              variant="outlined"
              InputProps={{
                endAdornment: formData.cin && (
                  <IconButton
                    size="small"
                    onClick={() => setFormData({ ...formData, cin: "" })}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Name"
              value={formData.name}
              onChange={handleChange("name")}
              variant="outlined"
              InputProps={{
                endAdornment: formData.name && (
                  <IconButton
                    size="small"
                    onClick={() => setFormData({ ...formData, name: "" })}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Select
              fullWidth
              label="Role"
              value={formData.role}
              onChange={handleChange("role")}
              variant="outlined"
            >
              <MenuItem value="">Any</MenuItem>
              <MenuItem value="doctor">Doctor</MenuItem>
              <MenuItem value="admin">Admin</MenuItem>
            </Select>
          </Grid>

          {/* Row 2 */}
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Phone"
              value={formData.phone}
              onChange={handleChange("phone")}
              variant="outlined"
              InputProps={{
                endAdornment: formData.phone && (
                  <IconButton
                    size="small"
                    onClick={() => setFormData({ ...formData, phone: "" })}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Email"
              value={formData.email}
              onChange={handleChange("email")}
              variant="outlined"
              InputProps={{
                endAdornment: formData.email && (
                  <IconButton
                    size="small"
                    onClick={() => setFormData({ ...formData, email: "" })}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Specialization"
              value={formData.specialization}
              onChange={handleChange("specialization")}
              variant="outlined"
              helperText="For doctors only"
            />
          </Grid>

          {/* Row 3 - Search Type */}
          <Grid item xs={12}>
            <InputLabel className="!mb-1">Search Type</InputLabel>
            <RadioGroup
              row
              value={formData.searchType}
              onChange={handleChange("searchType")}
              className="gap-4"
            >
              <FormControlLabel
                value="partial"
                control={<Radio size="small" />}
                label="Partial Match"
              />
              <FormControlLabel
                value="exact"
                control={<Radio size="small" />}
                label="Exact Match"
              />
            </RadioGroup>
          </Grid>
        </Grid>

        <Box className="flex justify-end gap-2 mt-4">
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<ClearIcon />}
            onClick={handleReset}
          >
            Clear
          </Button>
          <Button
            variant="contained"
            color="primary"
            type="submit"
            startIcon={<SearchIcon />}
          >
            Search
          </Button>
        </Box>
      </Box>
    </Paper>
  );
}