import { Box, TextField, Typography } from "@mui/material";
import { DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

export default function VisitDetails({ formData, setFormData }) {
  return (
    <Box className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200">
      <Typography variant="h6" component="h3" className="mb-2">
        Visit Information
      </Typography>

      <Box className="flex gap-4 flex-wrap">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DateTimePicker
            label="Visit Date & Time"
            value={new Date(formData.visit.visitDate || Date.now())}
            onChange={(newDate) => {
              formData.visit.visitDate = newDate.getTime();
              setFormData({ ...formData });
            }}
            sx={{
              minWidth: "250px",
              flex: 1,
            }}
          />
        </LocalizationProvider>
      </Box>

      <TextField
        label="Clinical Notes"
        placeholder="Enter visit notes, observations, and recommendations"
        multiline
        rows={4}
        variant="outlined"
        fullWidth
        value={formData.visit.notes}
        onChange={(e) => {
          formData.visit.notes = e.target.value;
          setFormData({ ...formData });
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            "& textarea": {
              minHeight: "60px",
            },
          },
        }}
      />
    </Box>
  );
}
