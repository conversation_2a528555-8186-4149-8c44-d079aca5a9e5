# Treatments API Routes

All routes require authentication (`authMiddleware`).

---

## POST `/`

**Create a new treatment**

- **Body Parameters:**

  - `folder` (required): Folder ID
  - `patient` (required): Patient ID
  - `start` (required): Start date/time
  - `title` (required): Treatment title
  - `end` (optional): End date/time
  - `description` (optional): Treatment description

- **Access:** Only doctors (not admin or patient)
- **Response:** `201 Created` with the new treatment object, or error

---

## GET `/search/title`

**Search treatments by title (and optionally by patient)**

- **Query Parameters:**

  - `title` (required, min 2 chars): Title to search for (partial match)
  - `patientId` (optional): Filter by patient ID

- **Response:** Array of matching treatments (limited by `QUERY_PAGE_SIZE_LIMIT`)

---

## GET `/search/folder/:folderId`

**Get all treatments for a folder**

- **URL Parameter:**

  - `folderId` (required): Folder ID

- **Response:** Array of treatments for the folder, with `prescriptions.medicin` populated

---

## PUT `/:treatmentId`

**Update a treatment's details**

- **URL Parameter:**

  - `treatmentId` (required): Treatment ID

- **Body Parameters:** (all optional)

  - `title`: New title
  - `description`: New description
  - `start`: New start date/time
  - `end`: New end date/time
  - `state`: New state

- **Access:** Only doctors (not admin or patient)
- **Response:** Updated treatment object, or error

---

## PUT `/prescriptions/:treatmentId`

**Update the prescriptions array for a treatment**

- **URL Parameter:**

  - `treatmentId` (required): Treatment ID

- **Body Parameters:**

  - `prescriptions`: Array of prescription objects (will replace the current prescriptions)

- **Access:** Only doctors (not admin or patient)
- **Response:** Updated treatment object, or error

---

**Note:** All routes use `forceQuitMiddleware` to restrict access for admin and patient roles (only doctors can use these endpoints).
