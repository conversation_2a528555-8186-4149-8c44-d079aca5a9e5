import Content from "./Sections/Content";
import Explorer from "./Sections/Explorer";
import UrlSearch from "./Sections/UrlSearch";
import { ResizableDiv } from "../../../../utils/ResizableDiv";

export default function WorkersExplorer() {
  return (
    <div style={{ width: "100vw", height: "100vh" }}>
      <UrlSearch />
      <ResizableDiv
        style={{
          width: "100%",
          height: "calc(100vh - 50px)",
          display: "flex",
        }}
      >
        <Explorer />
        <Content />
      </ResizableDiv>
    </div>
  );
}
