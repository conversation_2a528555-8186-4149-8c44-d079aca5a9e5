import { useEffect, useState } from "react";
import {
  PatientsDataStore,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import { searchTreatmentByTitleAPI } from "../../../../../api";
import {
  Autocomplete,
  Box,
  Button,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useFetchPatientData } from "../../../../../utils/costumeHook";

export default function TreatmentSelector({
  formData,
  setFormData,
  showFields,
  setShowFields,
}) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentSelectionState, setCurrentSelectionState] =
    visitsExplorerStore.useStore();
  const options =
    PatientsDataStore.useStore({ setter: false }).patient[
      currentSelectionState.patient
    ]?.folder?.treatments || [];
  useFetchPatientData();

  // Search for treatments when search query changes
  useEffect(() => {
    if (searchQuery.length < 2) {
      return;
    }
  }, [searchQuery]);

  // Determine if we should show additional fields
  const shouldShowFields = () => {
    // If no title, don't show fields
    if (!formData.treatement.title) return false;

    // Check if current title matches any existing treatment
    const isExisting = options.some(
      (option) => option.title === formData.treatement.title
    );

    return !isExisting;
  };

  const handleTitleChange = (event, newValue) => {
    console.log(newValue);
    // If selecting an existing treatment from dropdown
    if (newValue && typeof newValue !== "string") {
      console.log(newValue);

      setFormData({
        ...formData,
        treatement: {
          ...newValue,
          treatement_id: newValue._id,
          startDate: newValue.start || Date.now(),
          endDate: newValue.end || null,
          state: newValue.state || "O",
          prescriptions: [],
        },
      });
      const index = options.findIndex((e) => e._id == newValue._id);
      setCurrentSelectionState({
        treatement: index,
      });
      setSearchQuery(newValue.title);

      setShowFields(false);
    }
    // If typing free text
    else {
      if (!newValue) return;
      const title =
        typeof newValue === "string" ? newValue : event.target.value;
      setFormData({
        ...formData,
        treatement: {
          ...formData.treatement,
          title,
          treatement_id: "", // Clear ID since it's new
        },
      });
      setSearchQuery(title);
      setShowFields(true);
    }
  };

  const handleClearSelection = () => {
    setFormData({
      ...formData,
      treatement: {
        treatement_id: "",
        title: "",
        description: "",
        startDate: Date.now(),
        endDate: null,
        state: "O",
        prescriptions: [],
      },
    });
    setSearchQuery("");
    setShowFields(false);
  };

  return (
    <Box className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200">
      <Typography variant="h6" component="h3" className="mb-2">
        Treatment Details
      </Typography>

      <Box className="flex gap-4 flex-wrap">
        <Autocomplete
          sx={{
            minWidth: "300px",
            flex: 1,
          }}
          freeSolo
          options={options}
          getOptionLabel={(option) => option.title || option}
          inputValue={searchQuery}
          onInputChange={(event, newInputValue) => {
            setSearchQuery(newInputValue);
            handleTitleChange(event, newInputValue);
          }}
          value={formData.treatement.title}
          onChange={handleTitleChange}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Treatment Title"
              required
              helperText="Type to search existing treatments or enter a new one"
            />
          )}
          renderOption={(props, option) => (
            <li {...props} key={option._id}>
              <Box>
                <Typography>{option.title}</Typography>
                {option.description && (
                  <Typography variant="body2" color="text.secondary">
                    {option.description}
                  </Typography>
                )}
              </Box>
            </li>
          )}
          filterOptions={(options) => options} // Show all results from API
          clearOnEscape
        />

        {formData.treatement.title && (
          <Button
            onClick={handleClearSelection}
            variant="outlined"
            color="secondary"
            className="px-6 py-2 font-semibold"
          >
            Clear
          </Button>
        )}
      </Box>

      {/* Show additional fields when:
          1. No treatment is selected (title is empty), OR
          2. The entered title doesn't match any existing treatment
      */}

      <>
        <Box className="flex gap-4 flex-wrap">
          <TextField
            sx={{
              minWidth: "180px",
              flex: 1,
            }}
            label="Description"
            disabled={!showFields}
            value={formData.treatement.description}
            onChange={(e) => {
              setFormData({
                ...formData,
                treatement: {
                  ...formData.treatement,
                  description: e.target.value,
                },
              });
            }}
            variant="outlined"
          />
        </Box>

        <Box className="flex gap-4 flex-wrap">
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              disabled={!showFields}
              label="Start Date"
              value={new Date(formData.treatement.startDate || Date.now())}
              onChange={(newDate) => {
                setFormData({
                  ...formData,
                  treatement: {
                    ...formData.treatement,
                    startDate: newDate.getTime(),
                  },
                });
              }}
              sx={{
                minWidth: "180px",
                flex: 1,
              }}
            />
            <DatePicker
              disabled={!showFields}
              label="End Date"
              value={
                formData.treatement.endDate
                  ? new Date(formData.treatement.endDate)
                  : null
              }
              onChange={(newDate) => {
                setFormData({
                  ...formData,
                  treatement: {
                    ...formData.treatement,
                    endDate: newDate ? newDate.getTime() : null,
                  },
                });
              }}
              sx={{
                minWidth: "180px",
                flex: 1,
              }}
            />
          </LocalizationProvider>

          <Select
            disabled={!showFields}
            sx={{
              minWidth: "180px",
              flex: 1,
            }}
            labelId="state-label"
            value={formData.treatement.state}
            label="Treatment Status"
            onChange={(e) => {
              setFormData({
                ...formData,
                treatement: {
                  ...formData.treatement,
                  state: e.target.value,
                },
              });
            }}
          >
            <MenuItem value="O">Ongoing</MenuItem>
            <MenuItem value="C">Canceled</MenuItem>
            <MenuItem value="T">Terminated</MenuItem>
          </Select>
        </Box>
      </>
    </Box>
  );
}
