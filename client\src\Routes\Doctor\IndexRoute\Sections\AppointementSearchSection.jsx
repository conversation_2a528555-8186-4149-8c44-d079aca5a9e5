import { useState } from "react";
import SearchBar from "../../../../utils/SearchBar";
import AppointementSearchItem from "../_components/ClientSearchItem";
import { searchAppointementsAPI } from "../../../../api";
import { userDataStore } from "../../../../data";

export default function AppointementSearchSection() {
  const [search, setSearch] = useState("");
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const userData = userDataStore.useStore({ setter: false });

  const handleSearch = async (e) => {
    e.preventDefault();
    setLoading(true);
    const [err, data] = await searchAppointementsAPI({
      query: search,
      token: userData.token,
    });
    setLoading(false);
    if (err) return setResults([]);
    setResults(data || []);
  };

  return (
    <div className="w-[48%] h-[100%] bg-white flex flex-col border-r border-gray-200">
      <form onSubmit={handleSearch} className="w-full pt-4">
        <SearchBar
          inputProp={{
            value: search,
            onChange: (e) => setSearch(e.target.value),
            placeholder: "Search appointments by title, description, or CIN...",
          }}
          buttonProp={{ type: "submit", disabled: loading }}
        />
      </form>

      <ul className="overflow-auto h-full">
        {loading && (
          <li className="p-4 text-center text-gray-500">
            <div className="animate-pulse">Searching appointments...</div>
          </li>
        )}

        {!loading && results.length === 0 && (
          <li className="p-4 text-center text-gray-400">
            No appointments found
          </li>
        )}

        {results.map((item) => (
          <>
            <AppointementSearchItem
              {...item}
              key={item._id}
              title={item.title}
              description={item.description}
              cin={item.patientCIN}
              date={item.date}
              status={item.status} // Assuming your API returns a status field
            />
          </>
        ))}
      </ul>
    </div>
  );
}
