import React, { useEffect, useRef } from "react";

/**
 * A resizable div component.
 *
 * @param {React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
 *   seperator?: {
 *     width?: number;
 *     style?: string;
 *     color?: string;
 *     error_margin?: number;
 *   };
 * }} props
 * @returns {JSX.Element}
 */
export function ResizableDiv(props) {
  const { children, onMouseDown, style = {}, seperator, ...rest } = props;

  const divRef = useRef(null);
  const isResizing = useRef(false);
  const animationFrame = useRef(null);

  // Store original styles to revert later
  const originalBorder = useRef("");
  const originalCursor = useRef("");
  const originalUserSelect = useRef("");
  const originalUserSelectBody = useRef("");

  useEffect(() => {
    if(divRef.current){
        divRef.current.style.willChange = "width";
    }

const handleMouseMove = (e) => {
  if (!isResizing.current || !divRef.current) return;

  if (animationFrame.current) {
    cancelAnimationFrame(animationFrame.current);
  }

  animationFrame.current = requestAnimationFrame(() => {
    divRef.current.style.width = `${Math.max(e.clientX, 200)}px`;
  });
};

    const handleMouseUp = () => {
      if (!divRef.current) return;
      isResizing.current = false;

      // Restore original styles
      divRef.current.style.borderRight = originalBorder.current;
      divRef.current.style.cursor = originalCursor.current;
      divRef.current.style.userSelect = originalUserSelect.current;
      document.body.style.userSelect = originalUserSelectBody.current;
    };

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
      if (animationFrame.current) cancelAnimationFrame(animationFrame.current);
      if(divRef.current)divRef.current.style.willChange = "";

    };
  }, []);

  return (
    <div
      {...rest}
      ref={divRef}
      style={{ ...style }}
      onMouseDown={(e) => {
        if (!divRef.current) return;

        const rect = divRef.current.getBoundingClientRect();
        const errorMargin = seperator?.error_margin ?? 10;

        const isNearRightEdge =
          e.clientX >= rect.right - errorMargin &&
          e.clientX <= rect.right + errorMargin;

        if (isNearRightEdge) {
          isResizing.current = true;

          // Save original styles
          originalBorder.current = divRef.current.style.borderRight || "";
          originalCursor.current = divRef.current.style.cursor || "";
          originalUserSelect.current = divRef.current.style.userSelect || "";
          originalUserSelectBody.current = document.body.style.userSelect || "";

          // Apply active resizing styles
          const borderWidth = seperator?.width ?? 2;
          const borderStyle = seperator?.style ?? "solid";
          const borderColor = seperator?.color ?? "red";

          divRef.current.style.borderRight = `${borderWidth}px ${borderStyle} ${borderColor}`;
          divRef.current.style.cursor = "ew-resize";
          divRef.current.style.userSelect = "none";
          document.body.style.userSelect = "none";
        }

        if (onMouseDown) {
          onMouseDown(e);
        }
      }}
    >
      {children}
    </div>
  );
}
