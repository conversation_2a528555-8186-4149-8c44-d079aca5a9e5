import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  IconButton,
  Paper,
  FormControlLabel,
  Switch,
} from "@mui/material";
import { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { LoadingBarStore } from "../../../../../data";
import toast from "react-hot-toast";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { formatDate } from "../../../../../utils/dateFormater";
import { useDebounce } from "../../../../../utils/costumeHook";

// Inside your handleChange (assumed in scope), add basic validation like:
const validateInput = (name, value) => {
  if (
    name === "email" &&
    value &&
    !/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(value)
  ) {
    toast.error("Invalid email format");
  } else if (name === "phone" && value && !/^\+?\d{7,15}$/.test(value)) {
    toast.error("Invalid phone number");
  }
};

export default function AdvancedPatentSearchForm({ onSearch }) {
  const [searchCriteria, setSearchCriteria] = useState({
    cin: "",
    name: "",
    phone: "",
    email: "",
    birthDate: Date.now(),
    searchType: "exact", // 'exact' or 'partial'
  });
  const debouncer = useDebounce(3000);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setSearchCriteria({ ...searchCriteria, [name]: value });
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    LoadingBarStore.setCurrent({ loading: true });

    try {
      // Process search criteria
      const processedCriteria = {
        ...searchCriteria,
        // Add any processing logic here (e.g., date formatting)
      };

      // Call the onSearch callback with the search criteria
      await onSearch(processedCriteria);
    } catch (error) {
      toast.error("Error performing search");
      console.error(error);
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  };
  const handleClear = () => {
    setSearchCriteria({
      cin: "",
      name: "",
      phone: "",
      email: "",
      birthDate: "",
      searchType: "exact",
    });
  };
  console.log(searchCriteria);
  return (
    <Paper elevation={3} className="p-4 w-[70%]">
      <Box component="form" onSubmit={handleSubmit}>
        <Typography variant="h6" component="h3" className="!mb-4">
          Advanced Patent Search
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              autoFocus={true}
              label="CIN (National ID)"
              name="cin"
              value={searchCriteria.cin}
              onChange={handleChange}
              variant="outlined"
              InputProps={{
                endAdornment: searchCriteria.cin && (
                  <IconButton
                    size="small"
                    onClick={() =>
                      setSearchCriteria({ ...searchCriteria, cin: "" })
                    }
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={searchCriteria.name}
              onChange={handleChange}
              variant="outlined"
              InputProps={{
                endAdornment: searchCriteria.name && (
                  <IconButton
                    size="small"
                    onClick={() =>
                      setSearchCriteria({ ...searchCriteria, name: "" })
                    }
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Phone Number"
              name="phone"
              value={searchCriteria.phone}
              onChange={(e) => {
                debouncer(() => validateInput("phone", e.target.value));
                handleChange(e);
              }}
              variant="outlined"
              InputProps={{
                endAdornment: searchCriteria.phone && (
                  <IconButton
                    size="small"
                    onClick={() =>
                      setSearchCriteria({ ...searchCriteria, phone: "" })
                    }
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={searchCriteria.email}
              onChange={(e) => {
                debouncer(() => validateInput("email", e.target.value));
                handleChange(e);
              }}
              variant="outlined"
              InputProps={{
                endAdornment: searchCriteria.email && (
                  <IconButton
                    size="small"
                    onClick={() =>
                      setSearchCriteria({ ...searchCriteria, email: "" })
                    }
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Date of Birth"
                value={
                  searchCriteria.birthDate
                    ? new Date(searchCriteria.birthDate)
                    : null
                }
                onChange={(value) => {
                  if (value) {
                    handleChange({
                      target: {
                        value: value.getTime(),
                        name: "birthDate",
                      },
                    });
                  }
                }}
                disableFuture
                slots={{
                  textField: (props) => (
                    <TextField
                      {...props}
                      fullWidth
                      value={
                        searchCriteria.birthDate
                          ? formatDate(new Date(searchCriteria.birthDate))
                          : "not specified"
                      }
                      onChange={() => {
                        handleChange({
                          target: {
                            name: "birthDate",
                            value: null,
                          },
                        });
                      }}
                    />
                  ),
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={searchCriteria.searchType === "exact"}
                  onChange={(e) =>
                    handleChange({
                      target: {
                        name: "searchType",
                        value: e.target.checked ? "exact" : "partial",
                      },
                    })
                  }
                />
              }
              label="Exact Match"
            />
          </Grid>
        </Grid>

        <Box className="flex justify-end gap-2 mt-4">
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<ClearIcon />}
            onClick={handleClear}
          >
            Clear
          </Button>
          <Button
            variant="contained"
            color="primary"
            type="submit"
            startIcon={<SearchIcon />}
          >
            Search
          </Button>
        </Box>
      </Box>
    </Paper>
  );
}
