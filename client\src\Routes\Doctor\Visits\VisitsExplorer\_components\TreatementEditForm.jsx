import {
  Box,
  Button,
  TextField,
  Typography,
  MenuItem,
  Select,
} from "@mui/material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useState } from "react";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import toast from "react-hot-toast";
import { updateTreatementAPI } from "../../../../../api";

export default function TreatementEditForm({ treatment }) {
  const [formData, setFormData] = useState({ ...treatment });
  const CurrentSelectionState = visitsExplorerStore.useStore({ setter: false });
  const [PatientsData, setPatientsData] = PatientsDataStore.useStore();
  const userData = userDataStore.useStore({ setter: false });
  const handleSubmit = async (e) => {
    LoadingBarStore.setCurrent({ loading: true });
    e.preventDefault();
    const [err, data] = await updateTreatementAPI(
      formData._id,
      formData,
      userData.token
    );
    if (err) {
      toast.error("Error updating treatment");
      LoadingBarStore.setCurrent({ loading: false });
      console.log(err);
      return;
    }
    console.log(data);
    //! NOT OVERWRITE THOSE
    delete data.prescriptions;
    delete data.visits;
    delete data.tests;
    //!
    const StoreTreatement =
      PatientsData.patient[CurrentSelectionState.patient].folder.treatments;
    StoreTreatement[CurrentSelectionState.treatement] = {
      ...StoreTreatement[CurrentSelectionState.treatement],
      ...data,
    };
    setPatientsData({ ...PatientsData });
    toast.success("Treatment updated successfully");

    PopUpElement.setCurrent({ element: () => null });
    LoadingBarStore.setCurrent({ loading: false });
    console.log("err");
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="mb-2">
        Edit Treatment
      </Typography>

      <TextField
        label="Title"
        fullWidth
        required
        value={formData.title}
        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
        variant="outlined"
      />

      <TextField
        label="Description"
        fullWidth
        multiline
        rows={3}
        value={formData.description}
        onChange={(e) =>
          setFormData({ ...formData, description: e.target.value })
        }
        variant="outlined"
      />

      <Box className="flex gap-4 flex-wrap">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Start Date"
            value={formData.start ? new Date(formData.start) : null}
            onChange={(newDate) => {
              setFormData({
                ...formData,
                start: newDate ? newDate.getTime() : null,
              });
            }}
            sx={{ flex: 1 }}
          />
          <DatePicker
            label="End Date (Optional)"
            value={formData.end ? new Date(formData.end) : null}
            onChange={(newDate) => {
              setFormData({
                ...formData,
                end: newDate ? newDate.getTime() : null,
              });
            }}
            sx={{ flex: 1 }}
          />
        </LocalizationProvider>
      </Box>

      <Select
        value={formData.state}
        label="Status"
        onChange={(e) => setFormData({ ...formData, state: e.target.value })}
        fullWidth
      >
        <MenuItem value="O">Ongoing</MenuItem>
        <MenuItem value="C">Canceled</MenuItem>
        <MenuItem value="T">Terminated</MenuItem>
      </Select>

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Save Changes
        </Button>
      </div>
    </Box>
  );
}
