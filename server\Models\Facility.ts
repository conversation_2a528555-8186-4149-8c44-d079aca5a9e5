//cabinet hospital clinic model
import { Schema, model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IFacility extends BaseModel {
  name: string; // Facility name
  type: "hospital" | "clinic" | "cabinet"; // You can expand this list
  address: string; // Street address
  phone: string | null; // Optional contact phone
  img: string | null; // Optional image/logo
  is_deleted?: boolean; // Soft delete flag
}

const facilitySchema = new Schema<IFacility>({
  name: { type: String, required: true },
  type: {
    type: String,
    enum: ["hospital", "clinic", "cabinet"],
    required: true,
  },
  address: { type: String, required: true },
  phone: { type: String, default: null },
  img: { type: String, default: null },
  createdAt: Number,
  updatedAt: Number,
  is_deleted: { type: Boolean, default: false }, // Soft delete flag
});

export const Facility = model<IFacility>(
  ModelRefs.Facility.modelName,
  facilitySchema
);
