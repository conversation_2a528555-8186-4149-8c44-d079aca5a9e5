import { ObjectId, Types } from "mongoose";
import { Visit } from "../Models/Visit";
import { User } from "../Models/User";
import { Folder } from "../Models/Folder";
import { Treatment } from "../Models/Treatment";
import { roles } from "./roles";
import { hashPassword } from "./passwordHash";

export async function createVisit(
  notes: string,
  visitDate: Date,
  doctorId: string
) {
  const visit = new Visit({ date: visitDate, notes, doctor: doctorId });
  await visit.save();
  return visit;
}

export async function getOrCreatePatientUser(
  patientCIN: string,
  userData: {
    cin: string; // Assuming CIN is the ID required
    name: string[]; // Full name split into parts
    birthDay: number; // Unix timestamp for birthday
    email?: string;
    phone?: string | null;
    img?: string | null;
    hospital?: string | null;
    gender?: "F" | "M";
    specialization?: string;
  }
) {
  const searchBy: Record<string, any> = {};
  if (patientCIN) searchBy.cin = patientCIN;
  const user = await User.findOneAndUpdate(
    searchBy,
    {
      $setOnInsert: {
        name: userData.name,
        email: userData.email || userData.cin,
        phone: userData.phone || null,
        img: userData.img || null,
        gender: userData.gender || "M", // Default to 'M' if gender is not provided
        BirthDay: userData.birthDay,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        password: hashPassword(userData.cin), // Hash the CIN for password
        cin: userData.cin,
        profiles: [
          {
            type: roles.patient, // Default type is patient
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
      },
    },
    { upsert: true, new: true }
  );

  return user;
}

export async function getOrCreateFolder(patientId: string) {
  const searchBy: Record<string, any> = { patient: patientId };

  const folder = await Folder.findOneAndUpdate(
    searchBy,
    { $setOnInsert: { patient: patientId, treatments: [], start: Date.now() } },
    { upsert: true, new: true }
  );
  return folder;
}

export async function createNewTreatmentWithVisitAndUpdatePatientFolder({
  patientId,
  doctorId,
  visit,
  startDate,
  endDate,
  folder,
  state,
  prescriptions,
  title,
  description,
}: {
  patientId: string;
  doctorId: string;
  visit: InstanceType<typeof Visit>; // Visit document type
  startDate: number;
  endDate: number | null;
  folder: InstanceType<typeof Folder>; // Folder document type
  state: string;
  prescriptions: string[];
  title: string;
  description: string;
}) {
  const treatment = new Treatment({
    visits: [visit._id],
    patient: patientId,
    folder: folder._id,
    doctor: doctorId,
    start: startDate,
    end: endDate,
    state,
    prescriptions,
    title,
    description,
  });
  console.log(prescriptions);
  await treatment.save();
  folder.treatments.push(treatment._id);
  await folder.save();

  return treatment;
}

export async function addVisitToExistingTreatment(
  treatmentId: string | Types.ObjectId,
  visit: any, // Visit document type
  doctorId?: Types.ObjectId
): Promise<any | null> {
  const treatment = await Treatment.findById(treatmentId);
  if (!treatment) throw new Error("treatment not found");
  if (!doctorId) {
    treatment.visits.push(visit._id);
  } else {
    if (treatment.doctor !== doctorId) {
      throw new Error("Doctor does not match the treatment's doctor");
    }
    treatment.visits.push(visit._id);
  }
  await treatment.save();

  return treatment;
}
export async function getDoctorAssociatedVisits(
  doctorId: string,
  bettwen: { start: number; end: number }
) {
  const visits = await Visit.find({
    doctor: doctorId,
    date: {
      $gte: bettwen.start,
      $lte: bettwen.end,
    },
  });
  return visits;
}
