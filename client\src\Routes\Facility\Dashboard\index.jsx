import { useState, useEffect } from 'react';
import {PieChart, Pie, Cell, LineChart, CartesianGrid, XAxis, YAxis, Tooltip, Line, } from 'recharts';
import { getFacilityDashboardMetricsAPI } from '../../../api';
import { userDataStore, LoadingBarStore } from '../../../data';
import { formatDate } from '../../../utils/dateFormater';
const MonthNumberToName=[
  "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
]
export default function FacilityDashboard() {
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);

  const userData = userDataStore.useStore({ setter: false });
  const setLoadingBar = LoadingBarStore.useStore({ getter: false });

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!userData.token) return;

      setLoadingBar({ loading: true });
      setError(null);

      try {
        const [apiError, data] = await getFacilityDashboardMetricsAPI(userData.token);

        if (apiError) {
          setError('Failed to load dashboard data. Please try again.');
          console.error('Dashboard API error:', apiError);
        } else {
          setDashboardData(data);
        }
      } catch (err) {
        setError('An unexpected error occurred.');
        console.error('Dashboard fetch error:', err);
      } finally {
        setLoadingBar({ loading: false });
      }
    };

    fetchDashboardData();
  }, [userData.token, setLoadingBar]);

  // Transform API data for components
  const transformedData = dashboardData ? {
    patients: transformPatientsData(dashboardData.patients),
    staff: transformStaffData(dashboardData.staff),
    facility: dashboardData.facility,
    facilityManager: dashboardData.facilityManager,
    medicalRecords: transformMedicalRecordsData(dashboardData.medicalRecords)
  } : null;

  if (error) {
    return (
      <div className="p-6 max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 font-medium">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <PatientsCard data={transformedData?.patients} />
        <FacilityStaffCard data={transformedData?.staff} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FacilityDataCard data={transformedData?.facility} facilityManager={transformedData?.facilityManager} />
        <MedicalRecordsCard data={transformedData?.medicalRecords} />
      </div>
    </div>
  );
}
function FacilityStaffCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading staff data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-users text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.totalWorkers || 0}</p>
        </div>
      </div>

      {/* Staff Composition Bar Graph - Kept original but with matched styling */}
      <div className="px-6 py-4">
        <div className='h-[30px] w-full bg-amber-100 flex rounded-[20px] overflow-hidden hover:transform-[scale(1.03)]'>
          <div 
            className="admin h-full bg-blue-600 transition-all duration-500 " 
            style={{ width: `${(data.admins / data.totalWorkers) * 100}%` }}
          ></div>
          <div 
            className="doc h-full bg-pink-600 transition-all duration-500" 
            style={{ width: `${(data.doctors / data.totalWorkers) * 100}%` }}
          ></div>
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <div className="flex  gap-1">
          <p className="text-sm text-gray-600 font-medium">Doctors: {data.doctors || 0}</p> |
          <p className="text-sm text-gray-600 font-medium">Admins: {data.admins || 0}</p>
          <p className="text-sm text-gray-600 font-medium">total: {data.admins + data.doctors || 0}</p>
        </div>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Admins</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Doctors</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function FacilityDataCard({ data, facilityManager }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Facility Data</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading facility data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Facility Information</h2>
      </div>

      <div className="flex flex-col md:flex-row">
        {/* Facility Details - Left Side */}
        <div className="px-6 py-4 space-y-4 md:w-1/2">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-green-50 rounded-full">
              <i className="fas fa-hospital text-green-600 text-3xl"></i>
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-800">{data.name || 'N/A'}</h3>
              <p className="text-sm text-gray-500">{data.type || 'Facility type not available'}</p>
            </div>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fas fa-map-marker-alt text-gray-500 text-sm"></i>
              </div>
              <span className="text-gray-600">{data.address || 'Address not available'}</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fas fa-phone text-gray-500 text-sm"></i>
              </div>
              <span className="text-gray-600">{data.phone || 'Phone not available'}</span>
            </div>
            {data.createdAt && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <i className="fas fa-calendar text-gray-500 text-sm"></i>
                </div>
                <span className="text-gray-600">
                  Established: {formatDate(data.createdAt)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Facility Manager Details - Right Side */}
        {facilityManager && (
           <div className="px-6 py-4 space-y-4 md:w-1/2">
          <div className="flex items-center gap-4">
             <div className="p-3 bg-blue-50 rounded-full">
                <i className="fas fa-user-tie text-blue-600 text-3xl"></i>
              </div>
            <div>
              <h3 className="text-lg font-bold text-gray-800 flex items-center">
                {facilityManager.name.join(" ") || 'N/A'}
                {/* Gender Icon */}
                {(() => {
                  const genderData = {
                    M: { icon: "mars", color: "blue-600", label: "Male" },
                    F: { icon: "venus", color: "pink-600", label: "Female" },
                    default: { icon: "genderless", color: "gray-500", label: "Not specified" },
                  };

                  const { icon, color } = facilityManager.gender
                    ? genderData[facilityManager.gender] || genderData.default
                    : genderData.default;

                  return (
                    <i className={`fas fa-${icon} text-${color} ml-2 text-lg`}></i>
                  );
                })()}
              </h3>
              <p className="text-sm text-gray-500">Facility Manager</p>
            </div>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fa-solid fa-envelope text-gray-500 text-sm  aspect-square"></i>
              </div>
              <span className="text-gray-600 truncate" title={facilityManager.email || 'Address not available'}>{facilityManager.email || 'Address not available'}</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fas fa-phone text-gray-500 text-sm"></i>
              </div>
              <span className="text-gray-600">{facilityManager.phone || 'Phone not available'}</span>
            </div>
              {facilityManager.BirthDay && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <i className="fas fa-calendar text-gray-500 text-sm"></i>
                </div>
                <span className="text-gray-600">
               {formatDate(facilityManager.BirthDay)}
                </span>
              </div>
            )}
          </div>
        </div>
        )}
      </div>
    </div>
  );
}

function PatientsCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading patient data...</p>
        </div>
      </div>
    );
  }
console.log(data)
  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-[50%] aspect-square">
            <i className="fas fa-user-injured text-blue-600 text-3xl "></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.total || 0}</p>
        </div>

        <div className="w-[100px] h-[100px] transform hover:scale-105 transition-transform">
          <WomanManPieChart data={data.genderData} />
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <p className="text-sm text-gray-600 font-medium">{data.last7Days || 0} in last 7 days</p>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">F</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">M</span>
          </div>
        </div>
      </div>
    </div>
  );
}



function WomanManPieChart({ data }) {
  const COLORS = ['#3b82f6', '#ec4899'];

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="w-full h-full bg-gray-100 rounded-full flex items-center justify-center">
        <span className="text-gray-400 text-xs">No data</span>
      </div>
    );
  }

  return (
    <PieChart width={100} height={100}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={25}
        outerRadius={40}
        paddingAngle={2}
        dataKey="value"
        animationBegin={100}
        animationDuration={1000}
      >
        {data.map((_, index) => (
          <Cell
            key={`cell-${index}`}
            fill={COLORS[index % COLORS.length]}
            stroke="transparent"
            
        
          />
        ))}
      </Pie>
    </PieChart>
  );
}

// Data transformation functions
function transformPatientsData(patientsData) {
  if (!patientsData || !Array.isArray(patientsData) || patientsData.length === 0) {
    return null;
  }

  const data = patientsData[0];
  const genderCounts = data.countByGender || [];
  const last7DaysData = data.totalLast7Days || [];

  // Calculate total patients and gender breakdown
  const maleCount = genderCounts.find(g => g.gender === 'M')?.count || 0;
  const femaleCount = genderCounts.find(g => g.gender === 'F')?.count || 0;
  const total = maleCount + femaleCount;
  const last7Days = last7DaysData.length > 0 ? last7DaysData[0].count : 0;

  return {
    total,
    male: maleCount,
    female: femaleCount,
    last7Days,
    genderData: [
      { name: 'Female', value: femaleCount },
      { name: 'Male', value: maleCount }
    ]
  };
}

function transformStaffData(staffData) {
  if (!staffData || !Array.isArray(staffData)) {
    return null;
  }

  const adminCount = staffData.find(s => s.type === 'admin')?.count || 0;
  const doctorCount = staffData.find(s => s.type === 'doctor')?.count || 0;
  const totalWorkers = adminCount + doctorCount;

  return {
    totalWorkers,
    admins: adminCount,
    doctors: doctorCount
  };
}

function transformMedicalRecordsData(medicalRecordsData) {
  if (!medicalRecordsData || !Array.isArray(medicalRecordsData)) {
    return null;
  }

  // Calculate total records for last 7 days (simplified - using total for now)
  const totalRecords = medicalRecordsData.reduce((sum, record) => sum + record.count, 0);

  return {
    last7Days: totalRecords,
    monthlyData: medicalRecordsData
  };
}



function MedicalRecordsCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Medical Records</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading medical records data...</p>
        </div>
      </div>
    );
  }

  // Transform monthly data
  data.monthlyData = MonthNumberToName.map((item, index) => ({
    name: item,
    uv: data.monthlyData.find((item) => item.month === index + 1)?.count || 0
  }));

  return (
    <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Medical Records</h2>
      </div>

      <div className="py-4 flex justify-center">
        <MedicalRecordsChartCard data={data.monthlyData} />
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-4">
        <p className="text-sm text-gray-600 font-medium">Monthly records overview</p>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-gray-600">Records</span>
        </div>
      </div>
    </div>
  );
}

function MedicalRecordsChartCard({ data }) {
  if (!data) {
    return (
      <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-gray-500">Loading medical records data...</p>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center">
      <LineChart 
        width={500}
        height={250}
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="name" 
          tick={{ fill: '#6b7280' }}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <YAxis 
          tick={{ fill: '#6b7280' }}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: '#ffffff',
            borderColor: '#e5e7eb',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Line 
          connectNulls 
          type="monotone" 
          dataKey="uv" 
          stroke="#ad46ff" 
          strokeWidth={2} 
          
          dot={{ r: 4, fill: '#ad46ff' }}
          activeDot={{ r: 6, stroke: '#ad46ff', strokeWidth: 2, fill: '#ffffff' }}
        />
      </LineChart>
    </div>
  );
}






/**
 * 
 * ? Patient card needs:
 *  - number of patients for each gender from last month to now
 *  - total patients in last 7 days (not gender specific)
 * ? Staff card needs:
 *  - all category staff count as {type1:count,type2:count,...}
 * ? facility Card needs:
 * - name
 * - address
 * - phone
 * - image
 * - createdAt
 * - type
 * - facility manager name
 * - facility manager email
 * - facility manager phone
 * 
 * ? Medical records card needs:
 * - number of records( visits ) for each month of the selected Year
 */