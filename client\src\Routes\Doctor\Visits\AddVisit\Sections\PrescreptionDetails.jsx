import { useState, useEffect } from "react";
import {
  PatientsDataStore,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import { getMedicinByTitle } from "../../../../../api";
import {
  Autocomplete,
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  MedicalServices as MedicinIcon,
  CalendarToday as DateIcon,
} from "@mui/icons-material";
import toast from "react-hot-toast";

// Medicine Search Component
const MedicineSearch = ({ value, onChange, loading }) => {
  const userData = userDataStore.useStore({ setter: false });
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (value.length < 2) {
      setOptions([]);
      return;
    }

    const fetchMedicins = async () => {
      try {
        const [error, response] = await getMedicinByTitle(
          value,
          userData.token
        );
        if (!error && response?.data) {
          setOptions(response.data);
        }
      } catch (err) {
        console.error("Failed to fetch medicins:", err);
      }
    };

    const debounceTimer = setTimeout(fetchMedicins, 300);
    return () => clearTimeout(debounceTimer);
  }, [value, userData.token]);

  return (
    <Autocomplete
      freeSolo
      options={options}
      getOptionLabel={(option) => option.title || option}
      inputValue={value}
      onInputChange={(_, value) => onChange(value, "")}
      onChange={(_, newValue) => {
        if (newValue && typeof newValue === "object") {
          onChange(newValue.title, newValue._id);
        }
      }}
      loading={loading}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Medicine Name"
          placeholder="Search medicine..."
          fullWidth
        />
      )}
      renderOption={(props, option) => (
        <li {...props} key={option._id}>
          <Typography>{option.title}</Typography>
        </li>
      )}
      sx={{ mb: 2 }}
    />
  );
};

// Prescription Form Component
const PrescriptionForm = ({ onSubmit }) => {
  const [form, setForm] = useState({
    medicin_id: "",
    medicin_title: "",
    dose: "",
    date: Date.now(),
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    onSubmit(form);
    setForm({
      medicin_id: "",
      medicin_title: "",
      dose: "",
      date: Date.now(),
    });
  };

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Add New Prescription
      </Typography>

      <MedicineSearch
        value={form.medicin_title}
        onChange={(title, id) =>
          setForm({ ...form, medicin_title: title, medicin: id })
        }
        loading={loading}
      />

      <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
        <TextField
          label="Dosage"
          value={form.dose}
          onChange={(e) => setForm({ ...form, dose: e.target.value })}
          placeholder="e.g., 500mg twice daily"
          fullWidth
        />

        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Date"
            value={new Date(form.date)}
            onChange={(newDate) => {
              setForm({
                ...form,
                date: newDate.getTime(),
              });
            }}
            slotProps={{ textField: { fullWidth: true } }}
          />
        </LocalizationProvider>
      </Box>

      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={handleSubmit}
        disabled={!form.medicin_title || !form.dose}
        fullWidth
      >
        Add Prescription
      </Button>
    </Box>
  );
};

// Prescription Item Component
const PrescriptionItem = ({ prescription, onRemove }) => {
  return (
    <Card variant="outlined" sx={{ mb: 1 }}>
      <ListItem
        secondaryAction={
          <IconButton edge="end" onClick={onRemove}>
            <DeleteIcon color="error" />
          </IconButton>
        }
      >
        <ListItemAvatar>
          <Avatar sx={{ bgcolor: "primary.main" }}>
            <MedicinIcon />
          </Avatar>
        </ListItemAvatar>
        <ListItemText
          primary={prescription.medicin.title || prescription.medicin_title}
          secondary={
            <>
              <Typography variant="body2">{prescription.dose}</Typography>
              <Typography
                variant="caption"
                color="text.secondary"
                display="flex"
                alignItems="center"
                gap={0.5}
              >
                <DateIcon fontSize="small" />
                {new Date(prescription.date).toLocaleDateString()}
              </Typography>
            </>
          }
        />
      </ListItem>
    </Card>
  );
};

// Prescription List Component
export default function PrescriptionList({ formData, setFormData }) {
  const currentSelectionState = visitsExplorerStore.useStore({ setter: false });
  const prescriptions =
    PatientsDataStore.useStore({ setter: false }).patient[
      currentSelectionState.patient
    ].folder?.treatments[currentSelectionState.treatement]?.prescriptions || [];

  const handleAddPrescription = (newPrescription) => {
    setFormData({
      ...formData,
      treatement: {
        ...formData.treatement,
        prescriptions: [
          ...formData.treatement.prescriptions,
          {
            medicin: newPrescription.medicin || "",
            medicin_title: newPrescription.medicin_title,
            dose: newPrescription.dose,
            date: newPrescription.date,
          },
        ],
      },
    });
  };

  const handleRemovePrescription = (index) => {
    const updated = [...formData.treatement.prescriptions];
    updated.splice(index, 1);
    setFormData({
      ...formData,
      treatement: {
        ...formData.treatement,
        prescriptions: updated,
      },
    });
  };

  return (
    <Card variant="outlined" sx={{ borderRadius: 2 }}>
      <CardContent>
        <PrescriptionForm onSubmit={handleAddPrescription} />

        <Divider sx={{ my: 3 }} />

        <Typography variant="subtitle1" gutterBottom>
          Treatement Prescriptions
        </Typography>

        <List>
          {formData.treatement.prescriptions.map((prescription, index) => (
            <PrescriptionItem
              key={index}
              prescription={prescription}
              onRemove={() => handleRemovePrescription(index)}
            />
          ))}
          {prescriptions.map((e, index) => {
            return (
              <PrescriptionItem
                key={index}
                prescription={e}
                onRemove={() => {
                  toast.error("you can't delete a prescription");
                }}
              />
            );
          })}
        </List>
      </CardContent>
    </Card>
  );
}
