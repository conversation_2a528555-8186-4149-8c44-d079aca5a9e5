import readline from "readline";
import connectToMongoDB from "../database/connection";
import { Facility } from "../Models/Facility";
import * as env from "dotenv";
import path from "path";

// filepath: c:/Users/<USER>/Desktop/doctors/server/scripts/generate-hospital.ts

env.config({ path: path.join(__dirname, "..", ".env") });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const ask = (question: string): Promise<string> =>
  new Promise((resolve) => rl.question(question, resolve));

(async () => {
  await connectToMongoDB();

  console.log("\n👋 Let's create a new hospital:\n");

  const name = await ask("Hospital name: ");
  const facilityType = await ask(
    "Facility type (hospital | clinic | cabinet): "
  );
  const address = await ask("Address: ");
  const phone = await ask("Phone (optional): ");
  const img = await ask("Image URL (optional): ");

  const now = Math.floor(Date.now() / 1000);

  const hospitalData = {
    name: name.trim(),
    type: facilityType.trim(),
    address: address.trim(),
    phone: phone.trim() || null,
    img: img.trim() || null,
    createdAt: now,
    updatedAt: now,
  };

  try {
    const hospital = new Facility(hospitalData);
    await hospital.save();

    console.log("\n✅ Hospital successfully inserted into MongoDB:");
    console.log(JSON.stringify(hospital.toObject(), null, 2));
  } catch (err) {
    console.error("❌ Failed to insert hospital:", err);
  }
  rl.close();
})().then(() => {
  process.exit(0);
});
