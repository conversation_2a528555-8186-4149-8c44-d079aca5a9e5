# Database Models Documentation

- **Shared Properties (models.\* extended):**

* `createdAt`: int (unix timestamp) – The creation time of the record.
* `updatedAt`: int (unix timestamp) – The last update time of the record.

## Users Collection

- **\_id**: object_id – Unique identifier for a user.
- **name**: string[] – An array of names (e.g., `badr eddine`).
- **role**: enum (doctor, patient, sudo, admin) – The user's role.
- **email**: string – Email address (consider indexing).
- **phone**: string | null – Contact phone number.
- **img**: string | null – URL of the user's image.
- **hospital/cabinnet**: object_id | null – Reference to an associated hospital or cabinet.
- **gender**: enum (F, M) – Mapped to `Female` (F) or `Male` (M).
- **BirthDay**: int (unix timestamp) – The user's birth date.
- **specialization**: string – Area of expertise.

## Folders Collection

- **\_id**: object_id – Unique identifier for a folder.
- **patient**: object_id – Reference to the patient.
- **treatments**: object_id[] – Array of treatment identifiers (ref: treatments).
- **start**: int (unix timestamp) – Starting date/time.
- **notes**: string – Additional notes.

## Treatments Collection

- **\_id**: object_id – Unique identifier for a treatment.
- **visits**: object_id[] – Array of visit identifiers (ref: visits).
- **tests**: object_id[] – Array of test identifiers (ref: tests).
- **start**: int (unix timestamp) – Treatment start time.
- **doctor**: object_id - id of the doctor who do the treatement
- **end**: int (unix timestamp) | null – Treatment end time (can be null).
- **prescriptions**: Array of objects containing:
  - **medicin_id**: object_id – Reference to the medicin (ref: medicins).
  - **dose**: string – Prescription dosage.
  - **date**: int (unix timestamp) – Date of prescription.
- **state**: enum (O, C, T) – Represents the treatment status:
  - `O`: ongoing.
  - `C`: canceled.
  - `T`: terminated.

## Visits Collection

- **\_id**: object_id – Unique identifier for a visit.
- **date**: int (unix timestamp) – When the visit occurred.
- **notes**: string | null – Additional visit notes.
- _(Note: Additional fields might be added as the model is incomplete.)_

## Tests Collection

- **\_id**: object_id – Unique identifier for a test.
- **title**: string – Title of the test.
- **description**: string – Detailed description of the test.
- **files**: string[] – Array of file URLs (images, PDFs, scans).
- **state**: enum (C, S) – Represents the test status:
  - `C`: completed.
  - `S`: not-yet completed.

## Medicins Collection

- **\_id**: object_id – Unique identifier for a medicin.
- **title**: string – Title or name of the medicin.
- **description**: string – Description of the medicin.
- **img**: string – URL of the medicin image.
