# Backend Routes Documentation

---

## Middleware Documentation

### 1. authMiddleware

Ensures the request is from an authenticated user and injects a user object into the request as `req.user`.

### 2. forceQuitMiddleware

Enforces role-based access control by disallowing access for specific roles (admin and patient) to specific routes (e.g., `doctor/:id/profile`).

---

## Routes Documentation

### Route: `/login` (Login Router)

**Method:** **`POST`**

**Description:**  
Authenticates a user using email and password. The route validates provided credentials by hashing the password and comparing it with the stored hash.

**Request Body:**

- **email (string):** User's email address.
- **password (string):** User's plain text password.

**Response:**

- **200 OK:**
  ```json
  {
    "data": {
      /* UserObject */
    },
    "token": "JWT token string"
  }
  ```
- **400 Bad Request:** Missing email or password.
- **401 Unauthorized:** Invalid email or password.

---

### Route: `/appoinntements` (Appointment Routes)

**Note:** These routes require authentication through `authMiddleware` and have role restrictions enforced by `forceQuitMiddleware`. Refer to the Middleware Documentation above.

#### 1. POST `/`

**Description:**  
Creates a new appointment. Allowed for users with roles other than admin and patient (typically a doctor or sudo user).

**Request Body:**

- **Required:**
  - **title (string):** Appointment title.
  - **description (string):** Appointment description.
  - **date (UNIX INT):** Appointment date.
  - **doctor (string):** Doctor’s identifier (if the requester is a doctor, their token ID is used; otherwise (`sudo`), provided in the request).
- **Optional:**
  - **patient (ObjectID|null):** Patient’s identifier.
  - **patientCIN (string|null):** Patient’s identification number.
  - **additionalInfo (string):** Additional appointment information.
  - **notes (string):** Appointment notes.

**Response:**

- **201 Created:** Returns the newly created appointment object.
- **400 Bad Request:** Missing required fields or validation errors.

---

#### 2. GET `/`

**Description:**  
Retrieves all appointments associated with the authenticated doctor. Access is denied for `admin` and `patient` roles.

**Query Parameters (optional):**

- **start (number):** Only appointments with date >= start.
- **end (number):** Only appointments with date <= end.

**Response:**

- **200 OK:** Returns an array of appointment objects.
- **500 Internal Server Error:** An error occurred while processing the request.

---

#### 3. PUT `/:id`

**Description:**  
Updates an existing appointment identified by its ID. Update is allowed only for the doctor associated with the appointment or for a sudo user.

**Request Parameters:**

- **id (string):** Identifier of the appointment to update.

**Request Body:**

- **Required:**
  - **title (string):** Updated title.
  - **description (string):** Updated description.
  - **date (UNIX INT):** Updated appointment date.
- **Optional:**
  - **patient (ObjectID|null):** Updated patient identifier.
  - **patientCIN (string|null):** Updated patient CIN.
  - **additionalInfo (string):** Updated additional information.
  - **notes (string):** Updated notes.

**Response:**

- **200 OK:** Returns the updated appointment object.
- **400 Bad Request:** Missing required fields or invalid data.
- **404 Not Found:** No appointment found matching the criteria.

---

#### 4. DELETE `/:id`

**Description:**  
Deletes an appointment by its ID. Deletion is permitted only for the doctor who owns the appointment or for a sudo user.

**Request Parameters:**

- **id (string):** Identifier of the appointment to delete.

**Response:**

- **200 OK:** Confirms successful deletion with a success message.
- **500 Internal Server Error:** An error occurred during deletion.

---

### Route: `/visits/:id` (Get Visits for a Treatment)

**Method:** **`GET`**

**Description:**  
Retrieves paginated visits associated with a specific treatment.

- For patients, only visits related to their treatment are returned.
- For doctors, the treatment details are returned with secured sensitive data.

**Request Parameters:**

- **id (string):** Identifier of the treatment.

**Query Parameters:**

- **offset (number, optional):** Pagination offset (default is 0).

**Response:**

- **200 OK:**  
  Returns the treatment object with an array of paginated visits, for example:

  ```json
    [/* Array of visit details */],
  ```

- **500 Internal Server Error:**  
  Returns an error message indicating a backend error.

**Middleware:**

- Protected by `authMiddleware`.
- Access is restricted for admin users by `forceQuitMiddleware`.

---

### Route: `/visits/` (Create Visit)

**Method:** **`POST`**

**Description:**  
Creates a new visit record.

- If no treatment ID is provided, a new treatment is created and the visit is associated with it.
- If a treatment ID is provided, the visit is appended to the existing treatment.

**Request Body:**

- **Optional Treatment Identification:**

  - **treatement_id (string|null):** Identifier of an existing treatment. If not provided, a new treatment will be created.

- **Required for All Visits:**

  - **notes (string):** Visit notes.
  - **visitDate (number):** Visit date (defaults to current date if not provided).

- **Required Only When Creating a New Treatment (when treatement_id is not provided):**

  - **title (string):** Treatment title (human-friendly identifier).
  - **startDate (number):** Treatment start date (defaults to current date if not provided).

- **Optional Treatment Fields:**

  - **folder_id (string):** Identifier of the folder (used when creating a new treatment).
  - **doctor_id (string):** Doctor's identifier (used if the requester is not a doctor).
  - **endDate (number|null):** Treatment end date.
  - **state (string):** Treatment state ("O" for ongoing, "C" for canceled, "T" for terminated).
  - **prescriptions (array):** List of prescriptions.
  - **description (string):** Detailed description of the treatment (defaults to empty string).

- **Patient Identification:**
  - If adding to an existing treatment, no patient information is required.
  - If creating a new treatment AND the patient already exists in the system:
    - **patient.cin (string):** Patient's identification number.
  - If creating a new treatment AND the patient does not exist, provide a **patient** object with:
    - **cin (string):** Patient's identification number (required).
    - **name (string[]):** Patient's full name as an array (required).
    - **birthDay (number):** Patient's birth date as a UNIX timestamp (required).
    - **email, phone, img, gender**: (optional).

**Response:**

- **200 OK:**
  - If a new treatment is created, returns the treatment and visit objects:
    ```json
    {
      "treatment": {
        /* Newly created treatment object with title and description */
      },
      "visit": {
        /* Newly created visit object */
      }
    }
    ```
  - If appending to an existing treatment, returns the updated treatment and visit objects:
    ```json
    {
      "treatment": {
        /* Updated treatment object */
      },
      "visit": {
        /* Newly created visit object */
      }
    }
    ```
- **404 Not Found:**  
  If the specified treatment is not found when a treatment ID is provided.
- **400 Bad Request:**
  - If required visit data (notes) is missing.
  - If creating a new treatment and required treatment data (title) is missing.
  - If creating a new treatment and required patient data (cin, name, birthDay) is missing when creating a new patient.
- **500 Internal Server Error:**  
  Returns an error message indicating a backend error.

**Middleware:**

- Protected by `authMiddleware`.
- Access restricted for admin and patient roles via `forceQuitMiddleware`.

### Route: `/visits/between` (Get Visits Between Dates)

**Method:** **`GET`**

**Description:**  
Retrieves paginated visits for the authenticated doctor within a specified date range. Access is restricted for admin and patient roles.

**Query Parameters:**

- **startDate (number):**  
  (Required) Lower bound of the visits' dates.
- **endDate (number):**  
  (Optional) Upper bound of the visits' dates. Defaults to the current date.
- **doctor (string):**  
  (Optional) Doctor's identifier if the requester is not a doctor.

**Response:**

- **200 OK:**  
  Returns an array of visit details.
- **400 Bad Request:**  
  Returns an error if the required `startDate` query parameter is missing.
- **500 Internal Server Error:**  
  Returns an error message indicating a backend error.

**Middleware:**

- Protected by `authMiddleware`.
- Access restricted for `admin` and `patient` roles via `forceQuitMiddleware`.

---

### Route: `/treatements` (Treatment Routes)

**Note:** All routes require authentication (`authMiddleware`) and are restricted for `admin` and `patient` roles via `forceQuitMiddleware`.

#### 1. POST `/`

Creates a new treatment.

- **Body:**
  - `folder` (string, required): Folder ID
  - `patient` (string, required): Patient ID
  - `start` (number, required): Start date/time
  - `title` (string, required): Treatment title
  - `end` (number, optional): End date/time
  - `description` (string, optional): Description
- **Response:**
  - `201 Created`: Treatment object
  - `400/500`: Error message

#### 2. GET `/search/title`

Search treatments by title (and optionally by patient).

- **Query:**
  - `title` (string, required, min 2 chars)
  - `patientId` (string, optional)
- **Response:** Array of matching treatments

#### 3. GET `/search/folder/:folderId`

Get all treatments for a folder.

- **Params:**
  - `folderId` (string, required)
- **Response:** Array of treatments (with prescriptions.medicin populated)

#### 4. PUT `/:treatmentId`

Update a treatment's details.

- **Params:**
  - `treatmentId` (string, required)
- **Body:** Any of: `title`, `description`, `start`, `end`, `state`
- **Response:** Updated treatment object

#### 5. PUT `/prescriptions/:treatmentId`

Update the prescriptions array for a treatment.

- **Params:**
  - `treatmentId` (string, required)
- **Body:**
  - `prescriptions` (array): New prescriptions array
- **Response:** Updated treatment object

---

### Route: `/patient` (Patient Routes)

**Note:** All routes require authentication (`authMiddleware`).

#### 1. POST `/`

Create a new patient and their folder.

- **Body:**
  - `name` (string, required)
  - `cin` (string, required)
  - `email` (string, required)
  - `phone` (string, required)
  - `birthDate` (number, required)
  - `gender` (string, required)
- **Response:**
  - `201 Created`: Patient object (with folder)
  - `400/500`: Error message

#### 2. GET `/search/cin/:cin`

Search for a patient by CIN.

- **Params:**
  - `cin` (string, required)
- **Query:**
  - `page_number` (number, optional)
- **Response:** Array of matching patients

---

### Route: `/medecins` (Medecin Routes)

#### 1. GET `/title`

Search for medecins by title (case-insensitive).

- **Query:**
  - `title` (string, required)
- **Response:**
  - `200 OK`: `{ data: [ ...medecins ] }`
  - `400/404/500`: Error message
