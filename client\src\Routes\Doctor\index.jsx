import { Route, Routes } from "react-router-dom";
import IndexRouteDoctor from "./IndexRoute/index";
import VisitsRoute from "./Visits";
import DoctorDashboard from "./Dashboard";
import DoctorProfile from "./Profile";
import { _404 } from "../index";
export default function DoctorRoute() {
  return (
    <Routes>
      <Route index element={<IndexRouteDoctor />} />
      <Route path="dashboard" element={<DoctorDashboard />} />
      <Route path="profile" element={<DoctorProfile />} />
      <Route path="visits/*" element={<VisitsRoute />} />
      <Route path="*" element={<_404 />} />
    </Routes>
  );
}
