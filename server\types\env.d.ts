declare namespace NodeJS {
      interface ProcessEnv {
        "ALLUSERSPROFILE" :string;
        "ANDROID_HOME" :string;
        "APPDATA" :string;
        "APP_PORT" :string;
        "APP_BASE_URL" :string; // e.g. https://api.example.com
        "MAIL_HOST_SERVICE" :string; // e.g. GMAIL, HOTMAIL
        "MAIL_USER" :string; // mail account user
        "MAIL_PASS" :string; // mail account password
        "CHROME_CRASHPAD_PIPE_NAME" :string;
        "COLOR" :string;
        "COLORTERM" :string;
        "CommonProgramFiles" :string;
        "CommonProgramFiles(x86)" :string;
        "CommonProgramW6432" :string;
        "COMPUTERNAME" :string;
        "ComSpec" :string;
        "DriverData" :string;
        "EDITOR" :string;
        "EFC_856" :string;
        "FPS_BROWSER_APP_PROFILE_STRING" :string;
        "FPS_BROWSER_USER_PROFILE_STRING" :string;
        "GIT_ASKPASS" :string;
        "HOME" :string;
        "HOMEDRIVE" :string;
        "HOMEPATH" :string;
        "INIT_CWD" :string;
        "JAVA_HOME" :string;
        "JWT_SECRET" :string;
        "LANG" :string;
        "LOCALAPPDATA" :string;
        "LOGONSERVER" :string;
        "NODE" :string;
        "NODE_EXE" :string;
        "npm_command" :string;
        "npm_config_cache" :string;
        "npm_config_globalconfig" :string;
        "npm_config_global_prefix" :string;
        "npm_config_init_module" :string;
        "npm_config_local_prefix" :string;
        "npm_config_node_gyp" :string;
        "npm_config_noproxy" :string;
        "npm_config_npm_version" :string;
        "npm_config_prefix" :string;
        "npm_config_userconfig" :string;
        "npm_config_user_agent" :string;
        "npm_execpath" :string;
        "npm_lifecycle_event" :string;
        "npm_lifecycle_script" :string;
        "npm_node_execpath" :string;
        "npm_package_json" :string;
        "NPM_PREFIX_JS" :string;
        "NPM_PREFIX_NPX_CLI_JS" :string;
        "NPX_CLI_JS" :string;
        "NUMBER_OF_PROCESSORS" :string;
        "NVM_HOME" :string;
        "NVM_SYMLINK" :string;
        "OneDrive" :string;
        "ORIGINAL_XDG_CURRENT_DESKTOP" :string;
        "OS" :string;
        "PASSWORD_SALT" :string;
        "Path" :string;
        "PATHEXT" :string;
        "PROCESSOR_ARCHITECTURE" :string;
        "PROCESSOR_IDENTIFIER" :string;
        "PROCESSOR_LEVEL" :string;
        "PROCESSOR_REVISION" :string;
        "ProgramData" :string;
        "ProgramFiles" :string;
        "ProgramFiles(x86)" :string;
        "ProgramW6432" :string;
        "PROMPT" :string;
        "PSModulePath" :string;
        "PUBLIC" :string;
        "SESSIONNAME" :string;
        "SystemDrive" :string;
        "SystemRoot" :string;
        "TEMP" :string;
        "TERM_PROGRAM" :string;
        "TERM_PROGRAM_VERSION" :string;
        "TMP" :string;
        "TS_NODE_DEV" :string;
        "USERDOMAIN" :string;
        "USERDOMAIN_ROAMINGPROFILE" :string;
        "USERNAME" :string;
        "USERPROFILE" :string;
        "VSCODE_GIT_ASKPASS_EXTRA_ARGS" :string;
        "VSCODE_GIT_ASKPASS_MAIN" :string;
        "VSCODE_GIT_ASKPASS_NODE" :string;
        "VSCODE_GIT_IPC_HANDLE" :string;
        "VSCODE_INJECTION" :string;
        "windir" :string;
        "ZES_ENABLE_SYSMAN" :string
  }
}

