import { PopUpElement } from "../../../../../data";
import ContainerPopUp from "../../../../../utils/ContainerPopUp";
import { formatDate } from "../../../../../utils/dateFormater";
import VisitEditForm from "./VisitEditForm";

export default function ShowVisitData({ visit }) {
  if (!visit) return null;

  const visitDate = formatDate(visit.date);

  const handleEdit = () => {
    PopUpElement.setCurrent({
      element: () => (
        <ContainerPopUp>
          <VisitEditForm visit={visit} />
        </ContainerPopUp>
      ),
    });
  };

  return (
    <div className="border-t border-gray-300 pt-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <i className="fas fa-calendar-check text-blue-600"></i>
          Visit Details
        </h3>
        <button
          type="button"
          className="text-blue-500 hover:text-blue-700 transition-colors flex items-center gap-1"
          onClick={handleEdit}
          title="Edit Visit"
        >
          <i className="fas fa-edit"></i>
          <span className="text-sm hidden sm:inline">Edit</span>
        </button>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Visit Date */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="far fa-calendar text-gray-500 mr-2"></i>
              Date:
            </h4>
            <p className="text-gray-900 font-medium">
              <i className="fas fa-clock text-blue-500 mr-2"></i>
              {visitDate}
            </p>
          </div>

          {/* Visit Notes */}
          <div className="bg-white p-3 rounded-lg shadow-sm md:col-span-1">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="far fa-clipboard text-gray-500 mr-2"></i>
              Notes:
            </h4>
            <div className="text-gray-900 whitespace-pre-wrap bg-gray-50 p-3 rounded border border-gray-100 min-h-[60px]">
              {visit.notes ? (
                visit.notes
              ) : (
                <span className="text-gray-400 flex items-center">
                  <i className="far fa-comment-dots mr-2"></i>
                  No notes available
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
