import { PopUpElement } from "../../../../data";
import ShowPaitentAppointement from "./ShowPaitentAppointement";

export default function AppointementSearchItem({
  title,
  description,
  cin,
  date,
  status = "Scheduled",
  notes = "",
  additionalInfo = "",
}) {
  return (
    <li
      className="bg-white p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
      onClick={() => {
        PopUpElement.setCurrent({
          element: () => (
            <ShowPaitentAppointement
              appointement={{
                title,
                description,
                patientCIN: cin,
                date,
                notes,
                additionalInfo,
              }}
            />
          ),
        });
      }}
    >
      <div className="flex items-start gap-3">
        {/* Date/Time Indicator */}
        <div className="flex flex-col items-center min-w-[50px] bg-blue-50 rounded-lg p-2">
          <span className="text-xs font-medium text-blue-600 uppercase">
            {new Date(date).toLocaleString("default", { month: "short" })}
          </span>
          <span className="text-xl font-bold text-blue-800">
            {new Date(date).getDate()}
          </span>
        </div>

        {/* Appointment Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-baseline justify-between gap-2">
            <h3 className="text-base font-semibold text-gray-800 truncate">
              {title}
            </h3>
            <span
              className={`text-xs px-2 py-1 rounded-full ${
                status === "Completed"
                  ? "bg-green-100 text-green-800"
                  : status === "Cancelled"
                  ? "bg-red-100 text-red-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {status}
            </span>
          </div>

          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
            {description}
          </p>

          {cin && (
            <div className="mt-2 flex items-center gap-1 text-xs">
              <span className="text-gray-500">Patient CIN:</span>
              <span className="font-mono bg-gray-100 px-2 py-0.5 rounded text-gray-700">
                {cin}
              </span>
            </div>
          )}
        </div>
      </div>
    </li>
  );
}
