import readline from "readline";
import { Types } from "mongoose";
import { hashPassword } from "../utils/passwordHash";
import connectToMongoDB from "../database/connection";
import { User } from "../Models/User";
import * as env from "dotenv";
import path from "path";

env.config({ path: path.join(__dirname, "..", ".env") });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const ask = (question: string): Promise<string> =>
  new Promise((resolve) => rl.question(question, resolve));

(async () => {
  await connectToMongoDB();

  console.log("\n👋 Let's create your first user:\n");

  const fullName = await ask("Full name (e.g., <PERSON>): ");
  const nameParts = fullName.trim().split(" ");

  const role = await ask("Role (doctor/patient/sudo/admin): ");
  const email = await ask("Email: ");
  const phone = await ask("Phone (optional): ");
  const img = await ask("Image URL (optional): ");
  const hospitalId = await ask("Hospital ID (optional ObjectId): ");
  const gender = await ask("Gender (M/F): ");
  const birthDayStr = await ask("Birth date (YYYY-MM-DD): ");
  const specialization = await ask("Specialization (optional): ");
  const password = await ask("Password: ");
  const cin = await ask("CIN : ");
  const birthDate = new Date(birthDayStr);
  const birthTimestamp = Math.floor(birthDate.getTime() / 1000);
  const now = Math.floor(Date.now() / 1000);

  const hashedPassword = hashPassword(password); // await if it's async

  const userObject = {
    name: nameParts,
    email,
    phone: phone || null,
    img: img || null,
    gender,
    BirthDay: birthTimestamp,
    password: hashedPassword,
    createdAt: now,
    cin,
    updatedAt: now,
  };

  try {
    const user = new User(userObject);
    await user.save();

    console.log("\n✅ User successfully inserted into MongoDB:");
    console.log(JSON.stringify(user.toObject(), null, 2));
  } catch (err) {
    console.error("❌ Failed to insert user:", err);
  }
  rl.close();
})().then(() => {
  process.exit(0);
});
