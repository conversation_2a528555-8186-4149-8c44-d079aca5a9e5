import { useState, useEffect } from "react";
import {
  People as PeopleIcon,
  CalendarToday as CalendarIcon,
  LocalHospital as TreatmentIcon,
  Visibility as VisitIcon,
} from "@mui/icons-material";
import { CircularProgress } from "@mui/material";
import StatsCard from "./_components/StatsCard";
import RecentAppointments from "./_components/RecentAppointments";
import RecentPatients from "./_components/RecentPatients";
import MonthlyChart from "./_components/MonthlyChart";
import {
  getDashboardStatsAPI,
  getRecentAppointmentsAPI,
  getRecentPatientsAPI,
  getMonthlyStatsAPI,
} from "../../../api";

export default function DoctorDashboard() {
  const [stats, setStats] = useState(null);
  const [recentAppointments, setRecentAppointments] = useState([]);
  const [recentPatients, setRecentPatients] = useState([]);
  const [monthlyStats, setMonthlyStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");

      const [
        [statsErr, statsData],
        [appointmentsErr, appointmentsData],
        [patientsErr, patientsData],
        [monthlyErr, monthlyData],
      ] = await Promise.all([
        getDashboardStatsAPI(token),
        getRecentAppointmentsAPI(token, 5),
        getRecentPatientsAPI(token, 5),
        getMonthlyStatsAPI(token),
      ]);

      if (statsErr || appointmentsErr || patientsErr || monthlyErr) {
        setError("Failed to load dashboard data");
        return;
      }

      setStats(statsData.data);
      setRecentAppointments(appointmentsData.data);
      setRecentPatients(patientsData.data);
      setMonthlyStats(monthlyData.data);
    } catch (err) {
      console.error("Dashboard error:", err);
      setError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div
          className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
          role="alert"
        >
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="mb-6 text-2xl font-bold text-gray-800">
        Doctor Dashboard
      </h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-6">
        <StatsCard
          title="Total Patients"
          value={stats?.totalPatients || 0}
          icon={<PeopleIcon />}
          color="primary"
        />
        <StatsCard
          title="Today's Appointments"
          value={stats?.todayAppointments || 0}
          icon={<CalendarIcon />}
          color="secondary"
        />
        <StatsCard
          title="Total Treatments"
          value={stats?.totalTreatments || 0}
          icon={<TreatmentIcon />}
          color="success"
        />
        <StatsCard
          title="Recent Visits"
          value={stats?.recentVisits || 0}
          icon={<VisitIcon />}
          color="warning"
        />
      </div>

      {/* Charts and Lists */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Chart */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow p-6 h-full">
            <h2 className="text-xl font-semibold mb-4">Monthly Appointments</h2>
            <MonthlyChart data={monthlyStats} />
          </div>
        </div>

        {/* Recent Appointments */}
        <div>
          <div className="bg-white rounded-lg shadow p-6 h-full">
            <h2 className="text-xl font-semibold mb-4">Recent Appointments</h2>
            <RecentAppointments appointments={recentAppointments} />
          </div>
        </div>

        {/* Recent Patients */}
        <div>
          <div className="bg-white rounded-lg shadow p-6 h-full">
            <h2 className="text-xl font-semibold mb-4">Recent Patients</h2>
            <RecentPatients patients={recentPatients} />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow p-6 h-full">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg transition-colors">
                Schedule New Appointment
              </button>
              <button className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg transition-colors">
                Add New Patient
              </button>
              <button className="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg transition-colors">
                Create Treatment
              </button>
              <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-lg transition-colors">
                View All Visits
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
