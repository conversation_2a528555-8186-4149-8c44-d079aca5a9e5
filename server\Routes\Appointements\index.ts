import { Router } from "express";
import { Appointement } from "../../Models/Appointemente";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { tryCatch } from "../../utils/TryCatch";
import { logError } from "../../utils/logger";

const router = Router();
router.use(authMiddleware);

// Create a new appointment
router.post(
  "/",
  forceQuitMiddleware({
    admin: { reason: "you can't have appointement", HttpCode: 401 },
    patient: { reason: "you can't have appointement", HttpCode: 401 },
  }),
  async (req, res) => {
    const {
      title,
      description,
      date,
      patient = null,
      patientCIN = null,
      additionalInfo = "",
      notes = "",
    } = req.body;
    const doctor = req.user?.role === "doctor" ? req.user.id : req.body.doctor;
    if (!title || !description || !date || !doctor) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }

    const newAppointment = new Appointement({
      title,
      description,
      date,
      doctor,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      patient,
      patientCIN,
      additionalInfo,
      notes,
    });

    const [error, savedAppointment] = await tryCatch(newAppointment.save());
    if (error) {
      res.status(500).json({ error: "an error ocured" });
      logError(
        `Error creating appointment => DATA: ROUTE: /appointements ; METHOD: POST ; JWT: ${req.headers.authorization}`,
        error
      );
      return;
    }
    if (!savedAppointment) {
      res.status(500).json({ error: "appointement not saved" });
      return;
    }
    res.status(201).json(savedAppointment.toObject());
  }
);

// Get all appointments
router.get(
  "/",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "you cant know the doctor appoitements" },
    patient: { HttpCode: 401, reason: "you cant know the doctor appoitements" },
  }),
  async (req, res) => {
    const searchForDatesIfExist: Record<string, any> = {};
    if (req.query.start) {
      searchForDatesIfExist.date = { $gte: req.query.start };
    }
    if (req.query.end) {
      searchForDatesIfExist.date = searchForDatesIfExist.date || {};
      searchForDatesIfExist.date.$lte = req.query.end;
    }
    const [error, appointments] = await tryCatch(
      Appointement.find({
        doctor: req.user?.id,
        ...searchForDatesIfExist,
      })
    );
    if (error) {
      console.log(error);
      res.status(500).json({ error: error.message });
      return;
    }
    res.json(appointments);
  }
);

// Update an appointment by ID
router.put(
  "/:id",
  forceQuitMiddleware({
    admin: { reason: "you can't have appointement", HttpCode: 401 },
    patient: { reason: "you can't have appointement", HttpCode: 401 },
  }),
  async (req, res) => {
    const { title, description, date, patient, patientCIN } = req.body;
    if (!title || !description || !date) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }
    const updatedData = {
      title,
      description,
      date,
      patient: patient || null,
      patientCIN: patientCIN || null,
      additionalInfo: req.body.additionalInfo || "",
      notes: req.body.notes || "",
      updatedAt: Date.now(),
    };
    const updateFor = {
      doctor: {
        _id: req.params.id,
        doctor: req.user?.id,
      },
      sudo: {
        _id: req.params.id,
      },
    };

    const [error, appointment] = await tryCatch(
      Appointement.findOneAndUpdate(
        updateFor[req.user.role as keyof typeof updateFor],
        { $set: updatedData },
        { new: true }
      )
    );
    if (error) {
      res.status(400).json({ error: error.message });
      return;
    }

    res.json(appointment);
  }
);

// Delete an appointment by ID
router.delete(
  "/:id",
  forceQuitMiddleware({
    admin: { reason: "you can't have appointement", HttpCode: 401 },
    patient: { reason: "you can't have appointement", HttpCode: 401 },
  }),
  async (req, res) => {
    const deleteBy = {
      doctor: {
        _id: req.params.id,
        doctor: req.user?.id,
      },
      sudo: {
        _id: req.params.id,
      },
    };

    const [error] = await tryCatch(
      Appointement.deleteOne(deleteBy[req.user.role as keyof typeof deleteBy])
    );
    if (error) {
      console.log(error);
      res.status(500).json({ error: error.message });
      return;
    }
    res.json({ message: "Appointment deleted successfully" });
  }
);

router.get("/search", async (req, res) => {
  const { query, cin, description, title } = req.query;
  if (!query && !cin && !description && !title) {
    res.status(400).json({ error: "Query parameters is required" });
    return;
  }
  const searchQuery: Record<any, any> = {};
  if (query) {
    searchQuery.$or = [
      { title: { $regex: query, $options: "i" } },
      { description: { $regex: query, $options: "i" } },
      { patientCIN: { $regex: query, $options: "i" } },
    ];
  }
  if (cin) {
    searchQuery.patientCIN = { $regex: cin, $options: "i" };
  }
  if (description) {
    searchQuery.description = { $regex: description, $options: "i" };
  }
  if (title) {
    searchQuery.title = { $regex: title, $options: "i" };
  }
  try {
    const appointments = await Appointement.find({
      ...searchQuery,
      doctor: req.user?.id, // Ensure the user is only searching their own appointments
    }).lean();

    res.json(appointments);
  } catch (error) {
    console.error("Error searching appointments:", error);
    res.status(500).json({ error: "An error occurred while searching" });
  }
});
export default router;
