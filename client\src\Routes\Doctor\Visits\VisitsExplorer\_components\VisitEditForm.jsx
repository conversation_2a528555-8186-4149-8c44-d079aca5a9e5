import { Box, Button, TextField, Typography } from "@mui/material";
import { useState } from "react";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import toast from "react-hot-toast";
import { updateVisitAPI } from "../../../../../api";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

export default function VisitEditForm({ visit }) {
  const [formData, setFormData] = useState({ ...visit });
  const [patients, setPatients] = PatientsDataStore.useStore();
  const CurrentSelectionState = visitsExplorerStore.useStore({ setter: false });
  const userData = userDataStore.useStore({ setter: false });
  const handleSubmit = async (e) => {
    LoadingBarStore.setCurrent({ loading: true });
    e.preventDefault();
    const [err, data] = await updateVisitAPI(
      formData._id,
      formData,
      userData.token
    );
    if (err) {
      toast.error("Error updating visit");
      console.log(err);
      LoadingBarStore.setCurrent({ loading: true });

      return;
    }
    patients.patient[CurrentSelectionState.patient].folder.treatments[
      CurrentSelectionState.treatement
    ].visits[CurrentSelectionState.visit] = data;
    setPatients({ ...patients });
    toast.success("Visit updated successfully");
    PopUpElement.setCurrent({ element: () => null });
    LoadingBarStore.setCurrent({ loading: false });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="!mb-4">
        Edit Visit
      </Typography>

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DatePicker
          label="Visit Date"
          fullWidth
          required
          value={formData.date ? new Date(formData.date) : null}
          onChange={(newDate) => {
            setFormData({
              ...formData,
              date: newDate ? newDate.getTime() : null,
            });
          }}
          sx={{ flex: 1 }}
          variant="outlined"
        />
      </LocalizationProvider>
      <TextField
        label="Notes"
        multiline
        rows={4}
        fullWidth
        required
        value={formData.notes}
        onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
        variant="outlined"
        placeholder="Enter visit details..."
      />

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Save Changes
        </Button>
      </div>
    </Box>
  );
}
