import mongoose from "mongoose";

// Register empty schemas as placeholders, get info, then unregister
const emptySchema = new mongoose.Schema({});

const ensureModel = (name: string) => {
  let tempModel;
  let created = false;
  try {
    tempModel = mongoose.model(name);
  } catch {
    tempModel = mongoose.model(name, emptySchema);
    created = true;
  }
  const obj = {
    modelName: tempModel.modelName,
    collectionName: tempModel.collection.name,
  };
  if (created) {
    //@ts-ignore
    delete mongoose.connection.models[name];
  }
  return obj;
};

export const ModelRefs = {
  User: ensureModel("User"),
  Treatment: ensureModel("Treatment"),
  Visit: ensureModel("Visit"),
  Folder: ensureModel("Folder"),
  Test: ensureModel("Test"),
  Facility: ensureModel("Facility"),
  Medicin: ensureModel("Medicin"),
  Appointement: ensureModel("Appointement"),
};
