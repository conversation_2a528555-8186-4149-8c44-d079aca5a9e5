import { Box, Button } from "@mui/material";

export default function SubmitButton({ handleSubmit }) {
  return (
    <Box className="mt-8 flex justify-end">
      <Button
        variant="contained"
        color="primary"
        onClick={handleSubmit}
        size="large"
        className="px-6 py-3 font-semibold text-base"
        sx={{
          backgroundColor: "#1976d2", // primary main color
          "&:hover": {
            backgroundColor: "#1565c0", // primary dark color
          },
        }}
      >
        Create Visit
      </Button>
    </Box>
  );
}
