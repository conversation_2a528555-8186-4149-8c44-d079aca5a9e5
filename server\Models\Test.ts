import { Schema, model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface ITest extends BaseModel {
  title: string;
  description: string;
  files: string[];
  state: "C" | "S"; // C = completed, S = scheduled
}

const testSchema = new Schema<ITest>({
  title: { type: String, required: true },
  description: { type: String, default: "" },
  files: [{ type: String }],
  state: { type: String, enum: ["C", "S"], required: true },
  createdAt: Number,
  updatedAt: Number,
});

export const Test = model<ITest>(ModelRefs.Test.modelName, testSchema);
