export default function SearchBar({ inputProp = {}, buttonProp = {} }) {
  return (
    <div className=" px-1 flex items-center gap-[2%] justify-center mb-1">
      <input
        {...inputProp}
        className="px-4 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm 
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                  transition-all duration-200 placeholder-gray-400 w-[66%]"
        placeholder={
          inputProp.placeholder ||
          "Search appointments, patients, or records..."
        }
      />
      <button
        {...buttonProp}
        className="px-5 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                  disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200
                  whitespace-nowrap w-[29%]"
      >
        Search
      </button>
    </div>
  );
}
