import { WorkersDataStore, workersExplorerStore } from "../../../../../data";
import ShowWorkersList from "./ShowWorkersList";

export default function Renderer({ search, setSearch = () => {} }) {
  const [render, setRenderer] = workersExplorerStore.useStore();
  const workers = WorkersDataStore.useStore({ setter: false }).worker;
  
    return (
      <ShowWorkersList
        searchInput={search}
        workers={workers}
        onSelect={(i) => {
          setSearch("");
          setRenderer({ worker: i });
        }}
      />
    );
  

}
