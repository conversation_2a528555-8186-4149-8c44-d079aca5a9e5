import { Store } from "react-data-stores";
import { HomePageFor, userDataStore } from "../data";
import { useState } from "react";

export function IndexRoute() {
  const userData = userDataStore.useStore({ setter: false });
  useState(() => {
    console.log(window.location.search);
    if (userData.token && window.location.search == "") {
      Store.navigateTo(HomePageFor[userData.data.role]);
    }
  }, []);

  return <>redirect at the appropriate location based on the user data </>;
}

export function _404() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 px-4">
      <h1 className="text-9xl font-extrabold text-gray-300">404</h1>
      <p className="text-2xl md:text-3xl font-semibold text-gray-700 mt-4">
        Oops! Page not found.
      </p>
      <p className="text-gray-500 mt-2 mb-6 max-w-md text-center">
        The page you are looking for does not exist or has been moved.
      </p>
      <button
        onClick={() => Store.navigateTo("/")}
        className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow transition transform hover:scale-105 hover:shadow-lg hover:bg-blue-700"
      >
        Go Home
      </button>
    </div>
  );
}
