import jwt from "jsonwebtoken";
import { rolesType } from "./roles";

export type WorkerInvitePayload = {
  userId: string; // existing user's _id
  facilityId: string; // facility to join
  role: rolesType; // admin or doctor
  specialization?: string;
  inviterId: string; // facility manager user id
};

const INVITE_EXPIRATION = "48h"; // as requested

export function signWorkerInviteToken(payload: WorkerInvitePayload) {
  return jwt.sign(payload, process.env.JWT_SECRET as string, {
    expiresIn: INVITE_EXPIRATION,
  });
}

export function verifyWorkerInviteToken(token: string): WorkerInvitePayload {
  return jwt.verify(token, process.env.JWT_SECRET as string) as WorkerInvitePayload;
}

