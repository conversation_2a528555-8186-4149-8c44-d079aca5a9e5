import { useState } from "react";
import { LoadingBarStore, PopUpElement } from "../../../../../data";
import { createTestAPI } from "../../../../../api";
import toast from "react-hot-toast";

export default function TestAddForm({ treatmentId }) {
  const [formData, setFormData] = useState({ title: "", description: "", state: "S" });

  const handleChange = (field) => (e) => setFormData({ ...formData, [field]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    LoadingBarStore.setCurrent({ loading: true });
    try {
      const [err, data] = await createTestAPI(
        treatmentId,
        formData,
        localStorage.getItem("token")
      );
      if (err) {
        toast.error("Failed to add test");
        return;
      }
      toast.success("Test added successfully");
      PopUpElement.setCurrent({ element: () => null });
    } catch (ex) {
      console.error(ex);
      toast.error("Failed to add test");
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-3 w-[500px]">
      <div>
        <label className="block text-sm font-medium text-gray-700">Title</label>
        <input className="mt-1 w-full border rounded px-3 py-2" required value={formData.title} onChange={handleChange('title')} />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">Description</label>
        <textarea className="mt-1 w-full border rounded px-3 py-2" value={formData.description} onChange={handleChange('description')} />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">State</label>
        <select className="mt-1 w-full border rounded px-3 py-2" value={formData.state} onChange={handleChange('state')}>
          <option value="S">Scheduled</option>
          <option value="C">Completed</option>
        </select>
      </div>
      <div className="flex justify-end gap-2">
        <button type="button" className="px-4 py-2 border rounded" onClick={() => PopUpElement.setCurrent({ element: () => null })}>Cancel</button>
        <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">Add</button>
      </div>
    </form>
  );
}

