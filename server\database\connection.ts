import mongoose from "mongoose";
import { Treatment } from "../Models/Treatment";
import { Visit } from "../Models/Visit";
import { Appointement } from "../Models/Appointemente";
import { Facility } from "../Models/Facility";
import { User } from "../Models/User";
import { Folder } from "../Models/Folder";
import { Test } from "../Models/Test";
import { Medicin } from "../Models/Medicin";

const MONGODB_URI = "mongodb://localhost:27017/doctors";

const connectToMongoDB = async (): Promise<void> => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB successfully.");
    await Treatment.findOne();
    await Visit.findOne();
    await Appointement.findOne();
    await Facility.findOne();
    await User.findOne();
    await Folder.findOne();
    await Test.findOne();
    await Medicin.findOne();
    console.log("created indexes for all models.");
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
    process.exit(1);
  }
};

export default connectToMongoDB;
