import { PatientsDataStore, visitsExplorerStore } from "../../../../../data";

import ShowPatientData from "../_components/ShowPatientData";
import ShowVisitData from "../_components/ShowVisitData";
import ShowTreatementData from "../_components/ShowTreatementData";
import ShowPrescriptionData from "../_components/ShowPrescriptionData";
import ShowTestsData from "../_components/ShowTestsData";
export default function Content({}) {
  const render = visitsExplorerStore.useStore({ setter: false });
  const patientsData = PatientsDataStore.useStore({ setter: false });

  // Default render when nothing is selected
  const rtHtml = (
    <div className="w-[70%] h-full p-5 flex items-center justify-center bg-gray-50 border-gray-200">
      <p className="text-gray-700 text-lg">Select a patient to see details.</p>
    </div>
  );

  // If no patient is selected, return default
  if (
    typeof render.patient !== "number" ||
    !patientsData.patient[render.patient]
  ) {
    return rtHtml;
  }

  // Get patient data
  const patient = patientsData.patient[render.patient];

  // Initialize treatment and visit variables
  let treatment = null;
  let visit = null;

  if (
    typeof render.treatement === "number" &&
    patient.folder.treatments[render.treatement]
  ) {
    treatment = patient.folder.treatments[render.treatement];
    if (
      treatment &&
      typeof render.visit === "number" &&
      treatment.visits[render.visit]
    ) {
      visit = treatment.visits[render.visit];
    }
  }

  return (
    <div className="w-[70%] h-full p-6 overflow-y-auto bg-white lack border-gray-200 shadow-sm">
      <div className="space-y-6">
        <ShowPatientData patient={patient} />
        {treatment && <ShowTreatementData treatment={treatment} />}
        {visit && <ShowVisitData visit={visit} />}
        {treatment && (
          <ShowTestsData tests={treatment.tests || []} treatmentId={treatment._id} />
        )}
        {treatment && <ShowPrescriptionData treatment={treatment} />}
      </div>
    </div>
  );
}
