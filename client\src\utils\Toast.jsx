// utils/customToasts.js
import { toast } from "react-hot-toast";

const iconMap = {
  success: "fa-circle-check",
  error: "fa-circle-xmark",
  warning: "fa-triangle-exclamation",
  info: "fa-circle-info",
};

const bgColorMap = {
  success: "#d1e7dd",
  error: "#f8d7da",
  warning: "#fff3cd",
  info: "#cff4fc",
};

const textColorMap = {
  success: "#0f5132",
  error: "#842029",
  warning: "#664d03",
  info: "#055160",
};

export const showToast = (type, message) =>
  toast.custom((t) => (
    <div
      className="toast-container"
      style={{
        background: bgColorMap[type],
        color: textColorMap[type],
        display: "flex",
        alignItems: "center",
        padding: "12px 16px",
        borderRadius: "8px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        minWidth: "240px",
        gap: "12px",
      }}
    >
      <i
        className={`fa-solid ${iconMap[type]}`}
        style={{ fontSize: "1.2rem" }}
      />
      <span style={{ flex: 1 }}>{message}</span>
      <button
        onClick={() => toast.dismiss(t.id)}
        style={{
          background: "transparent",
          border: "none",
          fontSize: "1rem",
          cursor: "pointer",
          color: textColorMap[type],
        }}
      >
        &times;
      </button>
    </div>
  ));

export const ToastType = {
  SUCESS: "success",
  ERROR: "error",
  WARNING: "warning",
  INFO: "info",
};
