import { PopUpElement } from "../../../../data";
import ContainerPopUp from "../../../../utils/ContainerPopUp";
import AddAppointementPopUp from "./AddAppointementPopUp";
/**
 *
 * @param {object} param0
 * @param {React.DOMAttributes<HTMLSpanElement>} param0.props
 * @returns
 */
export default function ({ props = {} }) {
  const setPopUpPage = PopUpElement.useStore({ getter: false });

  return (
    <span
      {...props}
      onClick={(e) => {
        if (props.onClick) props.onClick(e);
        setPopUpPage({
          element: () => (
            <ContainerPopUp>
              <AddAppointementPopUp />
            </ContainerPopUp>
          ),
        });
      }}
      className="p-[10px_12px] border border-black rounded-full cursor-pointer mt-2.5"
    >
      <i className="fa-solid fa-plus"></i>
    </span>
  );
}
