import {
  <PERSON>comple<PERSON>,
  <PERSON>,
  Button,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useEffect, useState } from "react";
import { Add as AddIcon } from "@mui/icons-material";
import {
  LoadingBarStore,
  PatientsDataStore,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import {
  getMedicinByTitle,
  updateTreatementPrescriptionAPI,
} from "../../../../../api";
import toast from "react-hot-toast";
export default function AddPrescription() {
  const [form, setForm] = useState({
    medicin_id: "",
    medicin_title: "",
    dose: "",
    date: Date.now(),
  });
  const loading = LoadingBarStore.useStore({ setter: false }).loading;
  const handleSubmit = async () => {
    console.log(form);
    if (!form.medicin_id)
      return toast.error("you can't add un authorized medecin to treatement");
    LoadingBarStore.setCurrent({ loading: true });
    const currentSelectionState = visitsExplorerStore.getCurrent();
    const treatement =
      PatientsDataStore.getCurrent().patient[currentSelectionState.patient]
        .folder.treatments[currentSelectionState.treatement];
    const prescriptions = treatement.prescriptions;
    const [err, data] = await updateTreatementPrescriptionAPI(
      treatement._id,
      {
        prescriptions: [
          ...prescriptions.map((e) => ({ ...e, medicin: e.medicin._id })),
          { ...form, medicin: form.medicin_id },
        ],
      },
      userDataStore.getCurrent().token
    );
    if (err) {
      LoadingBarStore.setCurrent({ loading: false });
      toast.error("error adding prescription");
      return;
    }
    //!update the list
    treatement.prescriptions = data.prescriptions;
    //!triger re render
    PatientsDataStore.setCurrent({ ...PatientsDataStore.getCurrent() });
    toast.success("medicin addedd successfully");
    console.log(err);
    LoadingBarStore.setCurrent({ loading: false });
  };

  return (
    <Box className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200">
      <Typography variant="subtitle1" gutterBottom>
        Add New Prescription
      </Typography>

      <MedicineSearch
        value={form.medicin_title}
        onChange={(title, id) =>
          setForm({ ...form, medicin_title: title, medicin_id: id })
        }
        loading={loading}
      />

      <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
        <TextField
          label="Dosage"
          value={form.dose}
          onChange={(e) => setForm({ ...form, dose: e.target.value })}
          placeholder="e.g., 500mg twice daily"
          fullWidth
        />

        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Date"
            value={new Date(form.date)}
            onChange={(newDate) => {
              setForm({
                ...form,
                date: newDate.getTime(),
              });
            }}
            slotProps={{ textField: { fullWidth: true } }}
          />
        </LocalizationProvider>
      </Box>

      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={handleSubmit}
        disabled={!form.medicin_title || !form.dose}
        fullWidth
      >
        Add Prescription
      </Button>
    </Box>
  );
}
const MedicineSearch = ({ value, onChange, loading }) => {
  const userData = userDataStore.useStore({ setter: false });
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (value.length < 2) {
      setOptions([]);
      return;
    }

    const fetchMedicins = async () => {
      LoadingBarStore.setCurrent({ loading: true });
      try {
        const [error, response] = await getMedicinByTitle(
          value,
          userData.token
        );
        if (!error && response?.data) {
          setOptions(response.data);
        }
      } catch (err) {
        toast.error("No medicin was found");
        console.error("Failed to fetch medicins:", err);
      } finally {
        LoadingBarStore.setCurrent({ loading: false });
      }
    };

    const debounceTimer = setTimeout(fetchMedicins, 300);
    return () => clearTimeout(debounceTimer);
  }, [value, userData.token]);

  return (
    <Autocomplete
      freeSolo
      options={options}
      getOptionLabel={(option) => option.title || option}
      inputValue={value}
      onInputChange={(_, value) => onChange(value, "")}
      onChange={(_, newValue) => {
        if (newValue && typeof newValue === "object") {
          onChange(newValue.title, newValue._id);
        }
      }}
      loading={loading}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Medicine Name"
          placeholder="Search medicine..."
          fullWidth
        />
      )}
      renderOption={(props, option) => (
        <li {...props} key={option._id}>
          <Typography>{option.title}</Typography>
        </li>
      )}
      sx={{ mb: 2 }}
    />
  );
};
