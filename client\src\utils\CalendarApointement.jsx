import { DateCalendar } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

export default function CalenderApointement({
  selectedDate = new Date(),
  setSelectedDate = () => console.log("not provided"),
}) {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateCalendar
        sx={{
          width: "100%",
          margin: "0",
          "& .MuiDayCalendar-weekContainer": {
            justifyContent: "space-evenly",
            margin: "10px 0",
          },
          "& .MuiDayCalendar-header": { justifyContent: "space-evenly" },
          "& .MuiPickersDay-root": {
            padding: "15px",
            margin: "0",
          },
        }}
        showDaysOutsideCurrentMonth
        defaultValue={selectedDate}
        onChange={(newDate) => setSelectedDate(newDate)}
        minDate={new Date("1980-01-01")}
        //3 years after the current system date is the max date
        maxDate={new Date(Date.now() + 365 * 3 * 24 * 60 * 60 * 1000)}
      />
    </LocalizationProvider>
  );
}
