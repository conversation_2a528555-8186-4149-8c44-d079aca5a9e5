export default function MonthlyChart({ data }) {
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">No data available</div>
    );
  }

  const maxValue = Math.max(...data.map((item) => item.count), 1);

  return (
    <div className="w-full">
      <div className="flex items-end justify-between h-64 px-4 py-2 bg-gray-50 rounded-lg">
        {data.map((item, index) => {
          const height = (item.count / maxValue) * 100;
          return (
            <div key={index} className="flex flex-col items-center flex-1 mx-1">
              <div className="flex flex-col items-center justify-end h-full w-full">
                <span className="text-xs text-gray-600 mb-1">{item.count}</span>
                <div
                  className="bg-blue-500 rounded-t-md w-full min-h-[4px] transition-all duration-300 hover:bg-blue-600"
                  style={{ height: `${Math.max(height, 4)}%` }}
                />
              </div>
              <span className="text-xs text-gray-700 mt-2">
                {months[item.month - 1]}
              </span>
            </div>
          );
        })}
      </div>

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-500">
          Monthly appointment distribution for {new Date().getFullYear()}
        </p>
      </div>
    </div>
  );
}
