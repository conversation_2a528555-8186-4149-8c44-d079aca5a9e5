import { Avatar } from "@mui/material";

export default function StatsCard({ title, value, icon, color = "primary" }) {
  const colorClasses = {
    primary: "bg-blue-500",
    secondary: "bg-purple-500",
    success: "bg-green-500",
    warning: "bg-orange-500",
    error: "bg-red-500",
  };

  const bgColorClass = colorClasses[color] || colorClasses.primary;

  return (
    <div className="h-full shadow-lg hover:shadow-xl transition-shadow rounded-lg bg-white">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-500 text-sm mb-1">{title}</p>
            <h3 className="text-2xl font-bold">{value}</h3>
          </div>
          <Avatar className={`${bgColorClass} text-white w-12 h-12`}>
            {icon}
          </Avatar>
        </div>
      </div>
    </div>
  );
}
