import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { Types } from "mongoose";
import { Treatment } from "../../Models/Treatment";
import { Test } from "../../Models/Test";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const multer: any = require("multer");
import * as fs from "fs/promises";
import path from "path";

const router = Router();
router.use(authMiddleware);
const upload = multer({ storage: multer.memoryStorage() });

// GET tests for a treatment
router.get(
  "/:treatmentId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
  }),
  async (req, res) => {
    const { treatmentId } = req.params;
    if (!Types.ObjectId.isValid(treatmentId)) {
      res.status(400).json({ error: "Invalid treatment ID" });
      return;
    }
    const treatment = await Treatment.findById(treatmentId).populate("tests").select({ tests: 1, _id: 0 }).lean();
    if (!treatment) {
      res.status(404).json({ error: "Treatment not found" });
      return;
    }
    res.json(treatment.tests || []);
  }
);

// POST create a test and attach to treatment
router.post(
  "/:treatmentId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
  }),
  async (req, res) => {
    const { treatmentId } = req.params;
    const { title, description = "", files = [], state = "S" } = req.body;

    if (!Types.ObjectId.isValid(treatmentId)) {
      res.status(400).json({ error: "Invalid treatment ID" });
      return;
    }
    if (!title) {
      res.status(400).json({ error: "title is required" });
      return;
    }

    const treatment = await Treatment.findById(treatmentId);
    if (!treatment) {
      res.status(404).json({ error: "Treatment not found" });
      return;
    }

    const test = new Test({ title, description, files, state, createdAt: Date.now(), updatedAt: Date.now() });
    await test.save();

    treatment.tests.push(test._id);
    await treatment.save();

    res.status(201).json(test);
  }
);

// PUT update a test
router.put(
  "/update/:testId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
  }),
  async (req, res) => {
    const { testId } = req.params;
    if (!Types.ObjectId.isValid(testId)) {
      res.status(400).json({ error: "Invalid test ID" });
      return;
    }

    const update: any = { updatedAt: Date.now() };
    const { title, description, files, state } = req.body;
    if (title !== undefined) update.title = title;
    if (description !== undefined) update.description = description;
    if (files !== undefined) update.files = files;
    if (state !== undefined) update.state = state;

    const test = await Test.findByIdAndUpdate(testId, update, { new: true }).lean();
    if (!test) {
      res.status(404).json({ error: "Test not found" });
      return;
    }
    res.json(test);
  }
);

// POST upload images for a completed test
router.post(
  "/upload/:testId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "you cant access this resource" },
    patient: { HttpCode: 401, reason: "you cant access this resource" },
  }),
  upload.array("images", 20),
  async (req, res) => {
    try {
      const { testId } = req.params;
      if (!Types.ObjectId.isValid(testId)) {
        res.status(400).json({ error: "Invalid test ID" });
        return;
      }
      const test = await Test.findById(testId);
      if (!test) {
        res.status(404).json({ error: "Test not found" });
        return;
      }
      if (test.state !== "C") {
        res.status(400).json({ error: "Images can only be uploaded for completed tests" });
        return;
      }

      // Find treatment to get patient id and treatment id
      const treatment = await Treatment.findOne({ tests: test._id }).lean();
      if (!treatment) {
        res.status(404).json({ error: "Parent treatment not found" });
        return;
      }
      const patientId = (treatment.patient as any).toString();
      const treatmentId = (treatment._id as any).toString();

      const files = ((req as any).files as any[]) || [];
      if (!files.length) {
        res.status(400).json({ error: "No images provided" });
        return;
      }
      const uploadsRoot = path.join(__dirname, "..", "..", "uploads");
      const testsDir = path.join(uploadsRoot, "tests");
      await fs.mkdir(testsDir, { recursive: true });

      // Starting index from existing files length
      let startIndex = Array.isArray(test.files) ? test.files.length : 0;
      const savedPaths: string[] = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const now = Date.now();
        const imgNumber = startIndex + i; // 0-based index as requested
        const ext = (file.originalname && ("." + (file.originalname.split(".").pop() || ""))) || "";
        const name = `${now}_${patientId}_${treatmentId}_${imgNumber}${ext}`;
        const absPath = path.join(testsDir, name);
        await fs.writeFile(absPath, file.buffer);
        const publicPath = `/uploads/tests/${name}`;
        savedPaths.push(publicPath);
        test.files.push(publicPath);
      }
      test.updatedAt = Date.now();
      await test.save();

      res.status(201).json({ files: savedPaths, test });
    } catch (e) {
      console.error("Upload test images error:", e);
      res.status(500).json({ error: "Failed to upload images" });
    }
  }
);

export default router;
