import { Router } from "express";
import { Medicin } from "../../Models/Medicin";
import { tryCatch } from "../../utils/TryCatch";
import { ModelRefs } from "../../Models/ModelRefs";

const router = Router();
router.get("/title", async (req, res) => {
  const { title } = req.query;
  if (!title) {
    res.status(400).json({ message: "Please provide a title" });
    return;
  }
  const [err, data] = await tryCatch(
    Medicin.find({ title: { $regex: title, $options: "i" } }).lean()
  );

  if (err) {
    res.status(500).json({ message: "Internal server error" });
    return;
  }
  if (!data || data.length === 0) {
    res.status(404).json({ message: "No medecins found with that title" });
    return;
  }
  res.json({ data });
});
export default router;
