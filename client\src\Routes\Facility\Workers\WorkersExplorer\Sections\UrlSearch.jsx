import { WorkersDataStore, workersExplorerStore } from "../../../../../data";

export default function UrlSearch() {
  const [workersExplorerState, setWorkersExplorerState] =
    workersExplorerStore.useStore();
  const workersData = WorkersDataStore.useStore({ setter: false });

  const renderWorkerPath = () => {
    if (typeof workersExplorerState.worker !== "number") return null;

    const worker = workersData.worker[workersExplorerState.worker];
    const workerInfo = `${worker.name.join(" ")} (${worker.cin})`;

    return (
      <>
        <ChevronSeparator />
        <PathSegment
          label={workerInfo}
          onClick={() => {
            setWorkersExplorerState({
              ...workersExplorerState,
              worker: null,
            });
          }}
        />
      </>
    );
  };

  return (
    <div className="w-full h-12 px-12 flex items-center border-b border-gray-200 gap-2 text-base bg-white shadow-sm">
      <PathSegment
        label="Workers"
        icon="fa-solid fa-users"
        highlight
        onClick={() => {
          setWorkersExplorerState({
            worker: null,
          });
        }}
      />
      {renderWorkerPath()}
    </div>
  );
}

// Reusable component for path segments
const PathSegment = ({
  label,
  icon,
  highlight = false,
  onClick = () => {},
}) => (
  <div
    onClick={onClick}
    className={`flex items-center cursor-pointer ${
      highlight ? "text-blue-600" : "bg-blue-50 px-3 py-1 rounded-full"
    }`}
  >
    {icon && <i className={`${icon} mr-2`}></i>}
    <span
      className={`${highlight ? "font-medium" : "font-medium text-gray-700"}`}
    >
      {label}
    </span>
  </div>
);

// Reusable component for chevron separators
const ChevronSeparator = () => (
  <i className="fa-solid fa-chevron-right text-gray-400 mx-1"></i>
);
