import { Router } from "express";
import { verifyWorkerInviteToken } from "../../utils/workerInviteToken";
import { User } from "../../Models/User";
import { roles } from "../../utils/roles";

const router = Router();

// GET /facility/workers/join?token=...
router.get("/workers/join", async (req, res) => {
  try {
    const { token } = req.query as { token?: string };
    if (!token) {
      res.status(400).json({ error: "Missing token" });
      return;
    }
    const payload = verifyWorkerInviteToken(token);
    const { userId, facilityId, role, specialization } = payload;

    // Only allow roles admin/doctor per requirement (workers exclude sudo/patient/facilityManager)
    if (![roles.admin, roles.doctor].includes(role)) {
      res.status(400).json({ error: "Invalid role in token" });
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const exists = user.profiles.find(
      (p) => p.type === role && p.hospital?.toString() === facilityId.toString()
    );
    if (exists) {
      res.json({ message: "Already joined" });
      return;
    }

    user.profiles.push({
      type: role,
      hospital: (facilityId as unknown) as any,
      specialization: role === roles.doctor ? (specialization || "") : "",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    user.updatedAt = Date.now();
    await user.save();

    res.json({ message: "Joined successfully" });
  } catch (e: any) {
    if (e?.name === "TokenExpiredError") {
      res.status(400).json({ error: "Token expired" });
      return;
    }
    res.status(400).json({ error: "Invalid token" });
  }
});

export default router;

