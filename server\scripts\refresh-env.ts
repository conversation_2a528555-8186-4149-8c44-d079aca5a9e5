import * as env from "dotenv";
import * as fs from "fs/promises";
import path from "path";
env.config({ path: path.join(__dirname, "..", ".env") });
const envs = process.env;
fs.writeFile(
  path.join(__dirname, "..", "types", "env.d.ts"),
  `declare namespace NodeJS {
      interface ProcessEnv {
        ${Object.entries(envs)
          .map((env) => `"${env[0]}" :${typeof env[1]}`)
          .join(";\n        ")}
  }
}

`
);
