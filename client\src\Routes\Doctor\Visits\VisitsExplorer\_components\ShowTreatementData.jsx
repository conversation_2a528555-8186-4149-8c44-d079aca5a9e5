import { formatDate } from "../../../../../utils/dateFormater";
import { PopUpElement } from "../../../../../data";
import ContainerPopUp from "../../../../../utils/ContainerPopUp";
import TreatementEditForm from "./TreatementEditForm";

export default function ShowTreatementData({ treatment }) {
  if (!treatment) return null;

  const getStateIcon = () => {
    switch (treatment.state) {
      case "O":
        return (
          <i className="fas fa-spinner text-blue-500 animate-spin mr-2"></i>
        );
      case "T":
        return <i className="fas fa-ban text-red-500 mr-2"></i>;
      case "C":
        return <i className="fas fa-times-circle text-orange-500 mr-2"></i>;
      default:
        return null;
    }
  };

  const getStateClass = () => {
    switch (treatment.state) {
      case "O":
        return "bg-accent/10 border-accent";
      case "T":
        return "bg-danger/10 border-danger";
      case "C":
        return "bg-accent/20 border-accent";
      default:
        return "bg-nutral border-border";
    }
  };

  const startDate = formatDate(treatment.start);
  const endDate =
    treatment.state === "O" && treatment.end
      ? `${formatDate(treatment.end)} (estimated)`
      : treatment.state === "T" && treatment.end
      ? `${formatDate(treatment.end)} (terminated)`
      : treatment.state === "C" && treatment.end
      ? `${formatDate(treatment.end)} (canceled)`
      : treatment.end
      ? formatDate(treatment.end)
      : "Ongoing";

  const handleEdit = () => {
    PopUpElement.setCurrent({
      element: () => (
        <ContainerPopUp>
          <TreatementEditForm treatment={treatment} />
        </ContainerPopUp>
      ),
    });
  };

  return (
    <div className="border-t border-border pt-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-2xl font-bold text-revers flex items-center gap-2">
          <i className="fas fa-stethoscope text-primary"></i>
          Treatment: <span className="text-primary">{treatment.title}</span>
        </h2>
        <button
          type="button"
          className="text-blue-500 hover:text-blue-700 transition-colors flex items-center gap-1"
          onClick={handleEdit}
          title="Edit Treatment"
        >
          <i className="fas fa-edit"></i>
          <span className="text-sm hidden sm:inline">Edit</span>
        </button>
      </div>

      <div className={`p-4 rounded-lg border ${getStateClass()}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Status Card */}
          <div className="bg-card p-3 rounded-lg shadow-sm">
            <h3 className="font-semibold text-gray-800 mb-2 flex items-center">
              <i className="fas fa-info-circle text-gray-500 mr-2"></i>
              Status:
            </h3>
            <div className="text-gray-900 font-medium flex items-center">
              {getStateIcon()}
              {treatment.state === "O"
                ? "Ongoing"
                : treatment.state === "T"
                ? "Terminated"
                : "Canceled"}
            </div>
          </div>

          {/* Period Card */}
          <div className="bg-white lack p-3 rounded-lg shadow-sm">
            <h3 className="font-semibold text-gray-800 mb-2 flex items-center">
              <i className="far fa-calendar-alt text-gray-500 mr-2"></i>
              Period:
            </h3>
            <div className="text-gray-900">
              <span className="font-medium">{startDate}</span> -{" "}
              <span className={treatment.state !== "O" ? "font-medium" : ""}>
                {endDate}
              </span>
            </div>
          </div>
        </div>

        {/* Details Card */}
        <div className="mt-4 bg-white lack p-3 rounded-lg shadow-sm">
          <h3 className="font-semibold text-gray-800 mb-2 flex items-center">
            <i className="fas fa-align-left text-gray-500 mr-2"></i>
            Details:
          </h3>
          <p className="text-gray-900 whitespace-pre-wrap">
            {treatment.description || (
              <span className="text-gray-400">No details provided</span>
            )}
          </p>
        </div>
      </div>
    </div>
  );
}
