.app-content {
  width: calc(100% - 4rem);
  justify-self: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  top: 0;
  position: relative;
}
body {
  background-color: rgb(250, 250, 250);
}
#root .loading {
  width: 0;
  left: 0;
  height: 5px;
  background-color: rgb(120, 165, 245);
  position: absolute;
  top: 0;
  animation: loading_animation infinite 1.2s linear;
}
@keyframes loading_animation {
  50% {
    width: 50vw;
    left: 50%;
  }
  100% {
    left: 100%;
    width: 0;
  }
}
