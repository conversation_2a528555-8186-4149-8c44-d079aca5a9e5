import { PatientsDataStore, visitsExplorerStore } from "../../../../../data";
import { useFetchPatientData } from "../../../../../utils/costumeHook";
import ShowPatientsList from "./ShowPatientsList";
import ShowTreatementsList from "./ShowTreatementsList";
import ShowVisitsList from "./ShowVisitsList";

export default function Render({ search, setSearch = () => {} }) {
  const [render, setRendrer] = visitsExplorerStore.useStore();
  const patients = PatientsDataStore.useStore({ setter: false }).patient;
  useFetchPatientData();
  if (!render || typeof render.patient !== "number") {
    return (
      <ShowPatientsList
        searchInput={search}
        patients={patients}
        onSelect={(i) => {
          setSearch("");
          setRendrer({ patient: i });
        }}
      />
    );
  }
  if (!patients[render.patient].folder) {
    return <>patient has no medical history</>;
  }
  if (
    typeof render.visit !== "number" &&
    typeof render.treatement !== "number"
  ) {
    if (
      patients[render.patient].folder.treatments.length == 0 ||
      typeof patients[render.patient].folder.treatments[0] == "string"
    ) {
      //! it just wait until fetch hapend due to useFetchPatientTreatement doing the work
      return null;
    }
    return (
      <ShowTreatementsList
        searchInput={search}
        treatements={patients[render.patient].folder.treatments}
        onSelect={(i) => {
          setSearch("");
          setRendrer({ ...render, visit: undefined, treatement: i });
        }}
      />
    );
  }
  if (
    patients[render.patient].folder.treatments[render.treatement].visits
      .length == 0 ||
    typeof patients[render.patient].folder.treatments[render.treatement]
      .visits[0] == "string"
  ) {
    //TODO: fetch treatements based on folder.treatements[x].visits
    return null;
  }
  return (
    <ShowVisitsList
      searchInput={search}
      visits={
        patients[render.patient].folder.treatments[render.treatement].visits
      }
      onSelect={(i) => {
        setSearch("");
        setRendrer({ ...render, visit: i });
      }}
    />
  );
}
