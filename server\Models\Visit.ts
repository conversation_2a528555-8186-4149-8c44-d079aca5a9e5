import { ObjectId, Schema, model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IVisit extends BaseModel {
  date: number;
  notes: string | null;
  doctor: ObjectId;
  is_deleted?: boolean;
  facility: ObjectId;
  patient: ObjectId;
}

const visitSchema = new Schema<IVisit>({
  date: { type: Number, required: true },
  notes: { type: String, default: null },
  doctor: {
    type: Schema.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  createdAt: Number,
  is_deleted: { type: Boolean, default: false },
  updatedAt: Number,
  facility: {
    type: Schema.ObjectId,
    ref: ModelRefs.Facility.modelName,
    required: true,
  },
  patient: {
    type: Schema.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
});

// Index to speed up week activity queries by doctor/date
(visitSchema as any).index({ doctor: 1, date: 1, is_deleted: 1 });


export const Visit = model<IVisit>(ModelRefs.Visit.modelName, visitSchema);
