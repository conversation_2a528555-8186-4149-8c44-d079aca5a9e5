# RULES FOR AI TO FOLLOW
    - dont run build commends unless i tell you to (i dont need to build the app after each change)
    - if you need clarification of the requirement ask me to clarify
    - after each feature you build or update you need to update the documentation and the app.md file for it 
    - the documentation for back end routes is in the routes.md file
    - the documentation for the app is in the app.md file
    - the documentation for the development tracker is in the development-tracker.md file
    - the documentation for the bugs to deal with later is in the bugs to deal with later.md file 
    - the documentation for the database is in the database.md file
    - the documentation for the roles is in the roles.md file

 