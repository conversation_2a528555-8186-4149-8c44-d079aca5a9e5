import { Box, Button, TextField, Typography } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useState } from "react";
import { createVisitAPI } from "../../../../../api";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import toast from "react-hot-toast";

export default function VisitAddForm() {
  const [formData, setFormData] = useState({
    date: new Date(),
    notes: "",
  });
  const [patients, setPatients] = PatientsDataStore.useStore();
  const [CurrentSelectionState, setCurrentSelectionState] =
    visitsExplorerStore.useStore({});
  const userData = userDataStore.useStore({ setter: false });

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      date: date,
    });
  };

  const handleSubmit = async (e) => {
    LoadingBarStore.setCurrent({ loading: true });
    e.preventDefault();
    console.log(formData, patients.patient);
    formData.doctor = userData.data._id;
    formData.date = formData.date ? formData.date.getTime() : Date.now();
    const [err, data] = await createVisitAPI(
      formData,
      patients.patient[CurrentSelectionState.patient].folder.treatments[
        CurrentSelectionState.treatement
      ]._id,
      userData.token
    );
    if (err) {
      toast.error("error adding visit");
      console.log(err, patients.patient[CurrentSelectionState.patient]);
      LoadingBarStore.setCurrent({ loading: false });
      return;
    }
    toast.success("visit added succesfully");
    PopUpElement.setCurrent({ element: () => null });
    const visitIndex =
      patients.patient[CurrentSelectionState.patient].folder.treatments[
        CurrentSelectionState.treatement
      ].visits.push(data.data.visit) - 1;

    setPatients({ ...patients });
    setCurrentSelectionState({
      visit: visitIndex,
    });
    console.log(data);
    LoadingBarStore.setCurrent({ loading: false });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="!mb-4">
        New Visit
      </Typography>

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DatePicker
          label="Visit Date"
          value={formData.date}
          onChange={handleDateChange}
          renderInput={(params) => (
            <TextField
              {...params}
              fullWidth
              required
              variant="outlined"
            />
          )}
        />
      </LocalizationProvider>

      <TextField
        label="Notes"
        multiline
        rows={4}
        fullWidth
        required
        value={formData.notes}
        onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
        variant="outlined"
        placeholder="Enter visit details..."
      />

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Save Visit
        </Button>
      </div>
    </Box>
  );
}
