import { Schema, model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IMedicin extends BaseModel {
  title: string;
  description: string;
  img: string;
}

const medicinSchema = new Schema<IMedicin>({
  title: { type: String, required: true },
  description: { type: String, default: "" },
  img: { type: String, required: true },
  createdAt: Number,
  updatedAt: Number,
});

export const Medicin = model<IMedicin>(
  ModelRefs.Medicin.modelName,
  medicinSchema
);
