{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^2.16.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.3", "@mui/lab": "^6.0.0-beta.25", "@mui/material": "^6.4.3", "@mui/x-date-pickers": "^7.25.0", "axios": "^1.7.9", "date-fns": "^2.30.0", "notistack": "^3.0.2", "react-data-stores": "^1.4.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-mentions": "^4.4.10", "react-router-dom": "^6.29.0", "recharts": "^3.1.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/vite": "^4.1.10", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "tailwindcss": "^4.1.10", "vite": "^6.0.5"}}