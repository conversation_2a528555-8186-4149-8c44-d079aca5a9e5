import { But<PERSON>, TextField } from "@mui/material";
import ContainerPopUp from "./ContainerPopUp";
import { currentAppointement } from "../data";

export const Label = ({ color, bgColor, children }) => (
  <span
    style={{
      color: bgColor,
      backgroundColor: color,
      border: "1px solid " + bgColor,
    }}
    className="px-1.5 py-0.5 rounded-full text-[0.6rem] whitespace-nowrap font-bold"
  >
    {children}
  </span>
);

export function VisitPopUp() {
  return (
    <ContainerPopUp>
      <div className="w-[76%] h-[76%] bg-white rounded-lg shadow-md flex overflow-hidden m-auto text-black">
        <div className="w-1/2 p-5 border-r border-gray-300 box-border">
          <TextField
            label="date"
            type="datetime-local"
            style={{ width: "100%", margin: "10px 0 0 0" }}
            value={new Date().toISOString().split(".")[0]}
          />
        </div>
        <div className="flex flex-col gap-4 w-1/2 p-5 box-border overflow-y-auto">
          <TextField
            label="title"
            placeholder="Title"
            variant="outlined"
            fullWidth
          />
          <TextField
            label="description"
            placeholder="Description"
            variant="outlined"
            fullWidth
            multiline
            rows={3}
          />
          <TextField
            label="patient CIN"
            placeholder="patient  CIN (optional)"
            variant="outlined"
            value={currentAppointement.cin}
            fullWidth
          />
          <TextField
            label="additionalInfo"
            placeholder="Additional Info"
            variant="outlined"
            fullWidth
          />
          <TextField
            label="notes"
            placeholder="Notes"
            variant="outlined"
            fullWidth
            multiline
            rows={3}
          />
          <Button
            variant="outlined"
            onClick={() => {
              //! check the data.js export it's temporarly untill using the gobal stores;
              currentAppointement.cin = appointement.patientCIN;
              setPopUpPage({ element: () => <VisitPopUp /> });
            }}
          >
            visit
          </Button>
        </div>
      </div>
    </ContainerPopUp>
  );
}
