import { PatientsDataStore, visitsExplorerStore } from "../../../../../data";

export default function UrlSearch() {
  const [visitsExplorerState, setVisitsExplorerState] =
    visitsExplorerStore.useStore();
  const patientsData = PatientsDataStore.useStore({ setter: false });

  const renderPatientPath = () => {
    if (typeof visitsExplorerState.patient !== "number") return null;

    const patient = patientsData.patient[visitsExplorerState.patient];
    const patientInfo = `${patient.name.join(" ")} (${patient.cin})`;

    return (
      <>
        <ChevronSeparator />
        <PathSegment
          label={patientInfo}
          onClick={() => {
            setVisitsExplorerState({
              ...visitsExplorerState,
              visit: null,
              treatement: null,
            });
          }}
        />

        {typeof visitsExplorerState.treatement === "number" &&
          renderTreatmentPath(patient)}
      </>
    );
  };

  const renderTreatmentPath = (patient) => {
    if (typeof visitsExplorerState.treatement !== "number") return;
    const treatment = patient.folder.treatments[visitsExplorerState.treatement];
    const startDate = new Date(treatment.start).toLocaleDateString();
    const treatmentInfo = `${treatment.title} (${startDate})`;

    return (
      <>
        <ChevronSeparator />
        <PathSegment
          label={treatmentInfo}
          onClick={() => {
            setVisitsExplorerState({
              ...visitsExplorerState,
              visit: null,
            });
          }}
        />

        {typeof visitsExplorerState.visit === "number" &&
          renderVisitPath(treatment)}
      </>
    );
  };

  const renderVisitPath = (treatment) => {
    if (typeof visitsExplorerState.visit !== "number") return;

    const visitDate = new Date(
      treatment.visits[visitsExplorerState.visit].date
    ).toLocaleDateString();

    return (
      <>
        <ChevronSeparator />
        <PathSegment label={visitDate} />
      </>
    );
  };

  return (
    <div className="w-full h-12 px-12 flex items-center border-b border-gray-200 gap-2 text-base bg-white shadow-sm">
      <PathSegment
        label="Patients"
        icon="fa-solid fa-folder"
        highlight
        onClick={() => {
          setVisitsExplorerState({
            patient: null,
            visit: null,
            treatement: null,
          });
        }}
      />
      {renderPatientPath()}
    </div>
  );
}

// Reusable component for path segments
const PathSegment = ({
  label,
  icon,
  highlight = false,
  onClick = () => {},
}) => (
  <div
    onClick={onClick}
    className={`flex items-center cursor-pointer ${
      highlight ? "text-blue-600" : "bg-blue-50 px-3 py-1 rounded-full"
    }`}
  >
    {icon && <i className={`${icon} mr-2`}></i>}
    <span
      className={`${highlight ? "font-medium" : "font-medium text-gray-700"}`}
    >
      {label}
    </span>
  </div>
);

// Reusable component for chevron separators
const ChevronSeparator = () => (
  <i className="fa-solid fa-chevron-right text-gray-400 mx-1"></i>
);
