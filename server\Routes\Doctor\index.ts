import { Router } from "express";
import { User } from "../../Models/User";
import { Appointement as Appointemente } from "../../Models/Appointemente";
import { Treatment } from "../../Models/Treatment";
import { Visit } from "../../Models/Visit";
import { tryCatch } from "../../utils/TryCatch";
import { authMiddleware } from "../../middlewares/auth";

const router = Router();

// Apply auth middleware to all routes
router.use(authMiddleware);

// Get doctor dashboard statistics
router.get("/stats", async (req, res) => {
  try {
    const doctorId = req.user.id;

    // Get total patients count
    const [patientsErr, totalPatients] = await tryCatch(
      User.countDocuments({
        profiles: { $elemMatch: { type: "patient" } },
        is_deleted: { $ne: true },
      })
    );

    // Get today's appointments
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [appointmentsErr, todayAppointments] = await tryCatch(
      Appointemente.countDocuments({
        doctor: doctorId,
        date: { $gte: today.getTime(), $lt: tomorrow.getTime() },
        is_deleted: { $ne: true },
      })
    );

    // Get total treatments
    const [treatmentsErr, totalTreatments] = await tryCatch(
      Treatment.countDocuments({
        doctor: doctorId,
        is_deleted: { $ne: true },
      })
    );

    // Get recent visits count (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    const [visitsErr, recentVisits] = await tryCatch(
      Visit.countDocuments({
        doctor: doctorId,
        createdAt: { $gte: weekAgo.getTime() },
        is_deleted: { $ne: true },
      })
    );

    if (patientsErr || appointmentsErr || treatmentsErr || visitsErr) {
      res.status(500).json({ message: "Error fetching statistics" });
      return;
    }

    res.json({
      data: {
        totalPatients: totalPatients || 0,
        todayAppointments: todayAppointments || 0,
        totalTreatments: totalTreatments || 0,
        recentVisits: recentVisits || 0,
      },
    });
  } catch (error) {
    console.error("Dashboard stats error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get recent appointments
router.get("/recent-appointments", async (req, res) => {
  try {
    const doctorId = req.user.id;
    const limit = parseInt(req.query.limit as string) || 5;

    const [err, appointments] = await tryCatch(
      Appointemente.find({
        doctor: doctorId,
        is_deleted: { $ne: true },
      })
        .populate("patient", "name email phone")
        .sort({ date: -1 })
        .limit(limit)
        .lean()
    );

    if (err) {
      res.status(500).json({ message: "Error fetching appointments" });
      return;
    }

    res.json({ data: appointments || [] });
  } catch (error) {
    console.error("Recent appointments error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get recent patients
router.get("/recent-patients", async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 5;

    const [err, patients] = await tryCatch(
      User.find({
        profiles: { $elemMatch: { type: "patient" } },
        is_deleted: { $ne: true },
      })
        .select("name email phone createdAt")
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean()
    );

    if (err) {
      res.status(500).json({ message: "Error fetching patients" });
      return;
    }

    res.json({ data: patients || [] });
  } catch (error) {
    console.error("Recent patients error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get doctor profile
router.get("/profile", async (req, res) => {
  try {
    const doctorId = req.user.id;

    const [err, doctor] = await tryCatch(
      User.findById(doctorId)
        .populate("profiles.hospital", "name")
        .select("-password")
        .lean()
    );

    if (err) {
      res.status(500).json({ message: "Error fetching profile" });
      return;
    }

    if (!doctor) {
      res.status(404).json({ message: "Doctor not found" });
      return;
    }

    res.json({ data: doctor });
  } catch (error) {
    console.error("Doctor profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update doctor profile

// Get appointments for a specific date range
router.get("/appointments", async (req, res) => {
  try {
    const doctorId = req.user.id;
    const { start, end, page = 1, limit = 10 } = req.query;

    const query: any = {
      doctor: doctorId,
      is_deleted: { $ne: true },
    };

    if (start && end) {
      query.date = {
        $gte: parseInt(start as string),
        $lte: parseInt(end as string),
      };
    }

    const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

    const [err, appointments] = await tryCatch(
      Appointemente.find(query)
        .populate("patient", "name email phone cin")
        .sort({ date: -1 })
        .skip(skip)
        .limit(parseInt(limit as string))
        .lean()
    );

    const [countErr, total] = await tryCatch(
      Appointemente.countDocuments(query)
    );

    if (err || countErr) {
      res.status(500).json({ message: "Error fetching appointments" });
      return;
    }

    res.json({
      data: appointments || [],
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total: total || 0,
        pages: Math.ceil((total || 0) / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error("Appointments error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get treatments for the doctor
router.get("/treatments", async (req, res) => {
  try {
    const doctorId = req.user.id;
    const { page = 1, limit = 10, patientId } = req.query;

    const query: any = {
      doctor: doctorId,
      is_deleted: { $ne: true },
    };

    if (patientId) {
      query.patient = patientId;
    }

    const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

    const [err, treatments] = await tryCatch(
      Treatment.find(query)
        .populate("patient", "name email phone cin")
        .populate("folder", "name")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit as string))
        .lean()
    );

    const [countErr, total] = await tryCatch(Treatment.countDocuments(query));

    if (err || countErr) {
      res.status(500).json({ message: "Error fetching treatments" });
      return;
    }

    res.json({
      data: treatments || [],
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total: total || 0,
        pages: Math.ceil((total || 0) / parseInt(limit as string)),
      },
    });
  } catch (error) {
    console.error("Treatments error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get monthly appointment statistics
router.get("/monthly-stats", async (req, res) => {
  try {
    const doctorId = req.user.id;
    const { year = new Date().getFullYear() } = req.query;

    const startOfYear = new Date(parseInt(year as string), 0, 1).getTime();
    const endOfYear = new Date(
      parseInt(year as string),
      11,
      31,
      23,
      59,
      59
    ).getTime();

    const [err, monthlyStats] = await tryCatch(
      Appointemente.aggregate([
        {
          $match: {
            doctor: doctorId,
            date: { $gte: startOfYear, $lte: endOfYear },
            is_deleted: { $ne: true },
          },
        },
        {
          $group: {
            _id: { $month: { $toDate: "$date" } },
            count: { $sum: 1 },
          },
        },
        {
          $sort: { _id: 1 },
        },
      ])
    );

    if (err) {
      res.status(500).json({ message: "Error fetching monthly statistics" });
      return;
    }

    // Fill in missing months with 0
    const monthlyData = Array.from({ length: 12 }, (_, i) => {
      const monthData = monthlyStats?.find((stat) => stat._id === i + 1);
      return {
        month: i + 1,
        count: monthData ? monthData.count : 0,
      };
    });

    res.json({ data: monthlyData });
  } catch (error) {
    console.error("Monthly stats error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

export default router;
