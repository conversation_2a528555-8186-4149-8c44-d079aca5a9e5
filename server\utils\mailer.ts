/* Simple mailer utility around esay_mailer */
// Using CommonJS require due to package lacking TypeScript types
// eslint-disable-next-line @typescript-eslint/no-var-requires
const MailerPkg: any = require("esay_mailer");

type SendInviteEmailArgs = {
  to: string;
  facilityName: string;
  joinUrl: string;
};

function resolveHostService() {
  const key = (process.env.MAIL_HOST_SERVICE || "GMAIL").toUpperCase();
  // Try to resolve from built-in list, fallback to GMAIL
  const hosts = MailerPkg.HOSTS_DEFAULT_LIST || {};
  return hosts[key] || hosts.GMAIL;
}

function getMailerInstance() {
  const host_service = resolveHostService();
  const user = process.env.MAIL_USER;
  const pass = process.env.MAIL_PASS;
  return new MailerPkg({ host_service, user, pass });
}

export async function sendInviteEmail({ to, facilityName, joinUrl }: SendInviteEmailArgs) {
  const mailer = getMailerInstance();
  const subject = `Invitation to join ${facilityName}`;
  const html = {
    STRING_CODE: `
      <div style="font-family: Arial, sans-serif; line-height:1.5;">
        <p>Hello,</p>
        <p>A facility named <strong>${facilityName}</strong> has invited you to join as a worker.</p>
        <p>This link is valid for 48 hours.</p>
        <p style="margin-top:24px;">
          <a href="${joinUrl}" style="background:#2563eb;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;display:inline-block;">Join</a>
        </p>
        <p>If the button doesn't work, copy and paste this URL into your browser:</p>
        <p><a href="${joinUrl}">${joinUrl}</a></p>
      </div>
    `,
    DATA_TO_REPLACE: {},
  } as any;
  await mailer.sendEmail({ to, subject, html });
}

