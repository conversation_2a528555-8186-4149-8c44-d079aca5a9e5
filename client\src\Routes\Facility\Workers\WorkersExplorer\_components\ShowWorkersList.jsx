import { useState } from "react";
import { <PERSON>con<PERSON>utton, <PERSON><PERSON>, <PERSON>alogTitle, DialogContent, <PERSON>alogActions, But<PERSON> } from "@mui/material";
import { deleteWorkerAPI } from "../../../../../api";
import { WorkersDataStore, LoadingBarStore } from "../../../../../data";
import toast from "react-hot-toast";

export default function ShowWorkersList({ searchInput, workers, onSelect }) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [workerToDelete, setWorkerToDelete] = useState(null);

  const handleDeleteClick = (e, worker) => {
    e.stopPropagation(); // Prevent triggering the onSelect
    setWorkerToDelete(worker);
    setDeleteDialogOpen(true);
  };

  const handleDeleteWorker = async () => {
    if (!workerToDelete) return;

    LoadingBarStore.setCurrent({ loading: true });

    try {
      const [error] = await deleteWorkerAPI(
        localStorage.getItem("token"),
        workerToDelete._id
      );

      if (error) {
        toast.error(error?.response?.data?.error || "Failed to delete worker");
        return;
      }

      // Remove the worker from the store
      const currentWorkers = WorkersDataStore.getCurrent();
      currentWorkers.worker = currentWorkers.worker.filter(w => w._id !== workerToDelete._id);
      WorkersDataStore.setCurrent({ ...currentWorkers });

      toast.success("Worker deleted successfully!");
      setDeleteDialogOpen(false);
      setWorkerToDelete(null);
    } catch (err) {
      console.error("Error deleting worker:", err);
      toast.error("Failed to delete worker");
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  };
  const filteredWorkers = workers.filter((worker) => {
    if (!searchInput) return true;
    const searchLower = searchInput.toLowerCase();
    console.log(worker)
    return (
      worker.name.join(" ").toLowerCase().includes(searchLower) ||
      worker.cin.toLowerCase().includes(searchLower) ||
      worker.email.toLowerCase().includes(searchLower) ||
      (worker.phone && worker.phone.toLowerCase().includes(searchLower))
    );
  });

  if (filteredWorkers.length === 0) {
    return (
      <li className="text-center text-gray-500 py-4">
        {searchInput ? "No workers found" : "No workers available"}
      </li>
    );
  }

  return (
    <>
      {filteredWorkers.map((worker, index) => {
        const originalIndex = workers.findIndex((w) => w._id === worker._id);
        const workerProfile = worker.profiles.find(p => p.type === "admin" || p.type === "doctor");
        const roleDisplay = workerProfile?.type === "doctor" ? "Doctor" : "Admin";
        const specialization = workerProfile?.specialization || "";
        
        return (
          <li
            key={worker._id}
            onClick={() => onSelect(originalIndex)}
            className="p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md cursor-pointer transition-all duration-200"
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <i className={`fa-solid ${workerProfile?.type === "doctor" ? "fa-user-doctor" : "fa-user-tie"} text-blue-600`}></i>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {worker?.name?.join(" ")}
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      workerProfile?.type === "doctor"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-100 text-blue-800"
                    }`}>
                      {roleDisplay}
                    </span>
                    <IconButton
                      size="small"
                      onClick={(e) => handleDeleteClick(e, worker)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <i className="fas fa-trash text-xs"></i>
                    </IconButton>
                  </div>
                </div>
                <p className="text-sm text-gray-500 truncate">
                  CIN: {worker.cin}
                </p>
                {specialization && (
                  <p className="text-xs text-gray-400 truncate">
                    {specialization}
                  </p>
                )}
              </div>
            </div>
          </li>
        );
      })}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Delete Worker
        </DialogTitle>
        <DialogContent>
          {workerToDelete && (
          
              <p>
                Are you sure you want to delete <strong>{workerToDelete.name.join(" ")}</strong>?
              </p>
          
          
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setDeleteDialogOpen(false);
              setWorkerToDelete(null);
            }}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteWorker}
            color="error"
            variant="contained"
          >
            Delete Worker
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
