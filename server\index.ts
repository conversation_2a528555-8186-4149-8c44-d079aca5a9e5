import express from "express";
import * as dotEnv from "dotenv";
import LoginRouter from "./Routes/login/index";
import AppointementRoute from "./Routes/Appointements/index";
import cors from "cors";
import dbConnect from "./database/connection";
import VisitsRouter from "./Routes/Visits";
import PatientRouter from "./Routes/Patient";
import TreatementRouter from "./Routes/Treatements";
import medecinRouter from "./Routes/Medecins";
import DoctorRouter from "./Routes/Doctor";
import FacilityRouter from "./Routes/Facility";
import TestsRouter from "./Routes/Tests";
dotEnv.config();
const app = express();
app.use(cors());
// serve uploads statically
app.use("/uploads", express.static(require("path").join(__dirname, "uploads")));

app.use(express.json());
app.use("/login", LoginRouter);
app.use("/appoinntements", AppointementRoute);
app.use("/visits", VisitsRouter);
app.use("/patients", PatientRouter);
app.use("/treatments", TreatementRouter);
app.use("/tests", TestsRouter);
app.use("/medecins", medecinRouter);
app.use("/doctors", DoctorRouter);
app.use("/facility", FacilityRouter);
app.listen(process.env.APP_PORT, async () => {
  await dbConnect();
  console.log("app is running : http://localhost:" + process.env.APP_PORT);
});
