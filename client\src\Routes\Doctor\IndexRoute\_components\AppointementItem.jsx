export default function AppointmentItem({ time, title, description, LiProps }) {
  return (
    <li
      className="list-none bg-[#f8f9fa] my-1.5 py-2.5 px-4 flex items-center justify-between shadow-[0_2px_5px_rgba(0,0,0,0.1)] w-full cursor-pointer"
      {...LiProps}
    >
      <span className="bg-[#007bff] px-3 py-1.5 text-white text-xs font-bold rounded-[20px]">
        {time}
      </span>

      <div className="flex flex-col gap-1.5 px-2.5 flex-1">
        <span className="font-bold text-[1.05rem]">{title}</span>
        <span className="text-[0.7rem] pl-1 text-[#6c757d]">{description}</span>
      </div>

      <span className="ml-auto text-[#6c757d] text-xl cursor-pointer font-bold">
        <i className="fa-solid fa-angle-right"></i>
      </span>
    </li>
  );
}
