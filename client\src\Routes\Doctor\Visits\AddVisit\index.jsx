import { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import { createVisitAPI } from "../../../../api/index";

import {
  PatientsDataStore,
  userDataStore,
  visitsExplorerStore,
} from "../../../../data";
import { showToast, ToastType } from "../../../../utils/Toast";
import SubmitButton from "./_components/SubmitBtn";
import VisitDetails from "./Sections/VisitDetails";
import TreatmentSelector from "./Sections/TreatementSelector";
import PatientSelector from "./Sections/PatientSelector";
import PrescreptionDetails from "./Sections/PrescreptionDetails";

const steps = [
  "Patient Information",
  "Treatment Details",
  "Prescreption Details",
  "Visit Notes",
];

export default function AddVisitForm() {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    visit: { visitDate: Date.now(), notes: "" },
    treatement: {
      state: "O",
      prescriptions: [],
      startDate: Date.now(),
      title: "",
      treatement_id: "",
      description: "",
      endDate: null,
    },
    patient: {
      cin: "",
      firstName: "",
      lastName: "",
      birthDate: "",
      gender: "",
      email: "",
      phone: "",
    },
  });
  const [showPatientFields, setShowPatientFields] = useState(true);
  const [showTreatmentFields, setShowTreatmentFields] = useState(true);
  const userData = userDataStore.useStore({ setter: false });
  const handleNext = () => {
    // Validate current step before proceeding
    if (activeStep === 0 && !formData.patient.cin) {
      console.error("Patient CIN is required");
      return;
    }
    if (activeStep === 1 && !formData.treatement.title) {
      console.error("Treatment title is required");
      return;
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    if (!formData.visit.notes) {
      console.error("Visit notes are required");
      return;
    }
    // Prepare nested object for backend
    const payload = {
      patient: formData.patient,
      treatement: {
        ...formData.treatement,
        prescriptions: formData.treatement.prescriptions
          .filter((p) => p.medicin && p.dose && p.date)
          .map((p) => ({
            medicin: p.medicin || p.medicin_id,
            dose: p.dose,
            date: p.date,
          })),
      },
      visit: formData.visit,
    };
    const [err, data] = await createVisitAPI(payload, userData.token);
    if (err) {
      showToast(ToastType.ERROR, err?.response?.data?.error ?? err);
      return;
    }
    showToast(ToastType.SUCESS, "Visit created");
    window.location.reload();
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <PatientSelector
            formData={formData}
            setFormData={setFormData}
            showFields={showPatientFields}
            setShowFields={setShowPatientFields}
          />
        );
      case 1:
        return (
          <TreatmentSelector
            formData={formData}
            setFormData={setFormData}
            showFields={showTreatmentFields}
            setShowFields={setShowTreatmentFields}
          />
        );
      case 2:
        return (
          <PrescreptionDetails formData={formData} setFormData={setFormData} />
        );
      case 3:
        return <VisitDetails formData={formData} setFormData={setFormData} />;
      default:
        throw new Error("Unknown step");
    }
  };

  return (
    <div className="flex justify-center w-full ">
      <Paper className="w-[90%] max-w-[1200px] bg-white lack rounded-[12px] shadow-[0_8px_24px_rgba(0,0,0,0.1)] flex flex-col my-8 text-[#333] overflow-hidden ">
        {/* Header - exact match to original padding and border */}
        <Box className="py-[1.5rem] px-[2rem] bg-[#f8f9fa] border-b border-[#e0e0e0]">
          <Typography variant="h5" component="h2">
            New Patient Visit
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Complete all sections to create a new visit record
          </Typography>
        </Box>

        {/* Stepper */}
        <Box sx={{ width: "100%", p: 3 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Main content - precise spacing replication */}
        <Box className="p-[1rem] pt-0 flex flex-col gap-[1.5rem]">
          {getStepContent(activeStep)}

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            {activeStep !== 0 && (
              <Button onClick={handleBack} sx={{ mr: 1 }}>
                Back
              </Button>
            )}
            {activeStep < steps.length - 1 ? (
              <Button variant="contained" onClick={handleNext}>
                Next
              </Button>
            ) : (
              <SubmitButton handleSubmit={handleSubmit} />
            )}
          </Box>
        </Box>
      </Paper>
    </div>
  );
}
