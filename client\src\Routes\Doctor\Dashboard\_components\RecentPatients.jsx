import { Avatar } from "@mui/material";
import { Person as PersonIcon } from "@mui/icons-material";

export default function RecentPatients({ patients }) {
  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getInitials = (name) => {
    if (!name || !Array.isArray(name)) return "?";
    return name.map((n) => n.charAt(0).toUpperCase()).join("");
  };

  if (!patients || patients.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">No recent patients</div>
    );
  }

  return (
    <div className="max-h-80 overflow-y-auto divide-y divide-gray-100">
      {patients.map((patient, index) => (
        <div
          key={patient._id || index}
          className="flex items-start py-4 px-2 hover:bg-gray-50"
        >
          <div className="mr-3">
            <Avatar className="bg-green-100 text-green-600">
              {patient.img ? (
                <img
                  src={patient.img}
                  alt={patient.name?.join(" ")}
                  className="w-full h-full object-cover"
                />
              ) : (
                getInitials(patient.name)
              )}
            </Avatar>
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium truncate">
              {patient.name?.join(" ") || "Unknown Patient"}
            </h4>
            <div className="text-xs text-gray-500 space-y-0.5 mt-1">
              <p className="truncate">{patient.email || "No email"}</p>
              <p className="truncate">{patient.phone || "No phone"}</p>
              <p className="text-xs text-gray-400">
                Registered: {formatDate(patient.createdAt)}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
