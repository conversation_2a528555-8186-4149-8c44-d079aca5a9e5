import { Request, Response, NextFunction } from "express";
import { roles } from "../utils/roles";

declare type Rules = {
  [key in keyof typeof roles]: {
    condition?: (req: Request, res: Response) => boolean;
    reason: string;
    HttpCode: number;
  };
};
declare type Rule = Partial<Rules>;
export const forceQuitMiddleware = (rules: Rule) => {
  function forceQuit(req: Request, res: Response, next: NextFunction) {
    const props = Object.entries(rules);
    for (const rule of props) {
      if (req.user?.role !== rule[0]) continue;
      else {
        if (!rule[1].condition || !rule[1].condition(req, res)) {
          res.status(rule[1].HttpCode).json({ message: rule[1].reason });
          return;
        }
        return next();
      }
    }
    return next();
  }
  return forceQuit;
};
