import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { Treatment } from "../../Models/Treatment";
import { logError } from "../../utils/logger";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { roles } from "../../utils/roles";
import { Types } from "mongoose";
import {
  addVisitToExistingTreatment,
  getDoctorAssociatedVisits,
} from "../../utils/models_utils";
import { tryCatch } from "../../utils/TryCatch";
import { Visit } from "../../Models/Visit";
import { User } from "../../Models/User";

const router = Router();
router.use(authMiddleware);
// POST create visit into existing treatment
router.post(
  "/:treatmentId",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { treatmentId } = req.params;
    const { notes, date } = req.body;

    if (!Types.ObjectId.isValid(treatmentId)) {
      logError(`Invalid treatment ID: ${treatmentId}`, "Invalid ID");
      res.status(400).json({ error: "Invalid treatment ID" });
      return;
    }
    if (!notes || typeof notes !== "string") {
      res.status(400).json({ error: "Visit notes are required." });
      return;
    }
    const [doctorError, doctor] = await tryCatch(User.findById(req.user.id).lean());
    if (doctorError || !doctor) {
      res.status(401).json({ error: "Could not create visit. doctor not found." });
      return;
    }
    const [treatmentError, treatment] = await tryCatch(Treatment.findById(treatmentId).lean());
    if (treatmentError || !treatment) {
      res.status(404).json({ error: "Treatment not found." });
      return;
    }
    const visit = new Visit({
      notes,
      date: date || Date.now(),
      doctor: req.user.role === roles.doctor ? req.user.id : req.body.doctor,
      facility: doctor.profiles.find(p => p.type === roles.doctor)?.hospital,
      patient: treatment.patient,
    });
    await visit.save();

    const [err, treatement] = await tryCatch(
      addVisitToExistingTreatment(treatmentId, visit, visit.doctor as any)
    );

    if (err || !treatement) {
      logError(`Error adding visit to treatment ${treatmentId}`, err);
      visit.deleteOne();
      res.status(500).json({ error: "Could not create visit." });
      return;
    }

    res.status(201).json({ visit });
    return;
  }
);

// get visits bettwen two dates

router.get(
  "/between",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { startDate = null, endDate = Date.now() } = req.query;
    if (!startDate) {
      res.status(400).json({ error: "startDate is required" });
      return;
    }
    const doctor =
      req.user.role === roles.doctor ? req.user.id : req.query.doctor;
    const [err, data] = await tryCatch(
      getDoctorAssociatedVisits(doctor as any, {
        start: startDate as any,
        end: endDate as number,
      })
    );
    if (err || !data) {
      res.status(500).json({ error: "back_end_error" });
      logError(
        `Error in get visits for doctor id: ${req.user.id} => DATA: ROUTE: /treatment/between ; METHOD: GET ; JWT: ${req.headers.authorization}`,
        err
      );
      return;
    }
    res.status(200).json(data);
  }
);

// GET visits for a treatment
router.get(
  "/:id",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    // if the patient wants his own data, filter treatments by patient id

    const [err, treatement] = await tryCatch(
      Treatment.findById(req.params.id)
        .populate("visits")
        .select({ visits: 1, _id: 0 })
        .lean()
    );

    if (err || !treatement) {
      res.status(500).json({ error: "back_end_error" });
      logError(
        `Error in get visits for treatment id: ${req.params.id} => DATA: ROUTE: /visits/:id ; METHOD: GET ; JWT: ${req.headers.authorization}`,
        err
      );
      return;
    }
    res.status(200).json(treatement.visits);
  }
);
// GET visit by id
router.get(
  "/visit/:id",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const visitId = req.params.id;
    if (!Types.ObjectId.isValid(visitId)) {
      res.status(400).json({ error: "Invalid visit ID" });
      return;
    }

    const [err, visit] = await tryCatch(Visit.findById(visitId).lean());

    if (err || !visit) {
      res.status(404).json({ error: "Visit not found" });
      logError(
        `Error in get visit by id: ${visitId} => DATA: ROUTE: /visits/visit/:id ; METHOD: GET ; JWT: ${req.headers.authorization}`,
        err
      );
      return;
    }

    res.status(200).json(visit);
  }
);
// PUT update visit
router.put(
  "/:visitId",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { visitId } = req.params;
    const { notes, date } = req.body;
    if (!Types.ObjectId.isValid(visitId)) {
      res.status(400).json({ error: "Invalid visit ID" });
      return;
    }
    if (!notes || typeof notes !== "string") {
      res.status(400).json({ error: "Visit notes are required." });
      return;
    }
    const [err, visit] = await tryCatch(
      Visit.findOneAndUpdate(
        { _id: visitId, doctor: req.user.id, is_deleted: false },
        { notes, date: date || Date.now() },
        { new: true }
      )
    );
    if (err || !visit) {
      logError(`Error updating visit ${visitId}`, err);
      res.status(500).json({ error: "Could not update visit." });
      return;
    }
    res.status(200).json(visit);
  }
);

export default router;
