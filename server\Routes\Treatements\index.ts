import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { QUERY_PAGE_SIZE_LIMIT } from "../../utils/FLAGS";
import { Treatment } from "../../Models/Treatment";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { roles } from "../../utils/roles";

const router = Router();
router.use(authMiddleware);

router.post(
  "/",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { folder, patient, start, end, title, description } = req.body;

    if (!folder || !patient || !start || !title) {
      res.status(400).json({
        error: "folder, patient, start and title are required",
      });
      return;
    }

    const treatment = new Treatment({
      folder,
      patient,
      doctor: req.user.role == roles.doctor ? req.user.id : req.body.doctor,
      start,
      end: end || null,
      title,
      description: description || "",
    });

    try {
      await treatment.save();
      res.status(201).json(treatment);
    } catch (error) {
      res.status(500).json({ error: "Failed to create treatment" });
    }
  }
);
router.get("/search/title", async (req, res) => {
  const title = req.query.title as string | undefined;
  const patientId = req.query.patientId as string | undefined;

  if (!title || title.length < 2) {
    res.status(400).json({
      error: "title must be at least 2 characters long",
    });
    return;
  }

  const filter: Record<string, any> = {
    title: { $regex: title, $options: "i" },
  };

  if (patientId) {
    filter.patient = patientId;
  }

  const treatements = await Treatment.find(filter)
    .limit(QUERY_PAGE_SIZE_LIMIT)
    .select({
      title: 1,
      description: 1,
      start: 1,
      end: 1,
      state: 1,
      _id: 1,
    });

  res.json(treatements);
});
router.get("/search/folder/:folderId", async (req, res) => {
  const folder = req.params.folderId as string | undefined;

  if (!folder || typeof folder !== "string") {
    res.status(400).json({
      error: "folder id error",
    });
    return;
  }

  const treatements = await Treatment.find({
    folder: folder,
  })
    .populate("prescriptions.medicin")
    .lean();

  res.json(treatements);
});
// PUT update treatment
router.put(
  "/:treatmentId",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { treatmentId } = req.params;
    const { title, description, start, end, state } = req.body;
    if (!treatmentId) {
      res.status(400).json({ error: "treatmentId is required" });
      return;
    }
    const update: any = {};
    if (title) update.title = title;
    if (description) update.description = description;
    if (start) update.start = start;
    if (end || state == "T") {
      update.end = end || update.end || Date.now();
    }
    if (!end && state !== "T") {
      update.end = null;
    }

    if (state) update.state = state;
    try {
      const treatment = await Treatment.findOneAndUpdate(
        { _id: treatmentId, doctor: req.user.id, is_deleted: false },
        update,
        {
          new: true,
        }
      ).lean();
      if (!treatment) {
        res.status(404).json({ error: "Treatment not found" });
        return;
      }
      res.status(200).json(treatment);
    } catch (error) {
      res.status(500).json({ error: "Failed to update treatment" });
    }
  }
);
router.put(
  "/prescriptions/:treatmentId",
  forceQuitMiddleware({
    admin: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
    patient: {
      HttpCode: 401,
      reason: "you cant access this resource",
    },
  }),
  async (req, res) => {
    const { treatmentId } = req.params;
    if (!treatmentId) {
      res.status(400).json({ error: "treatmentId is required" });
      return;
    }
    //! Validate prescriptions
    const exsitingPrescriptions: string[] = [];
    if (req.body.prescriptions && Array.isArray(req.body.prescriptions)) {
      req.body.prescriptions = req.body.prescriptions.filter(
        (prescription: any) => {
          if (exsitingPrescriptions.includes(prescription.medicin))
            return false;
          if (!prescription.medicin) {
            res.status(400).json({ error: "medicin is required" });
            return false;
          }

          exsitingPrescriptions.push(prescription.medicin);
          return true;
        }
      );
    }

    try {
      const treatment = await Treatment.findOneAndUpdate(
        { _id: treatmentId, doctor: req.user.id, is_deleted: false },
        { $set: { prescriptions: req.body.prescriptions } },
        { new: true }
      )
        .populate("prescriptions.medicin")
        .lean();
      if (!treatment) {
        res.status(404).json({ error: "Treatment not found" });
        return;
      }
      res.status(200).json(treatment);
    } catch (error) {
      res.status(500).json({ error: "Failed to delete treatment" });
    }
  }
);
export default router;
