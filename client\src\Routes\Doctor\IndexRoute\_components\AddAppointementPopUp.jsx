import { useState } from "react";
import { userDataStore } from "../../../../data";
import { addAppointementAPI } from "../../../../api";
import { <PERSON><PERSON>, TextField } from "@mui/material";
import DateAppointementInput from "../../../../utils/DateAppointementInput";

export default function AddAppointementPopUp() {
  const userData = userDataStore.useStore({ setter: false });
  const [data, setData] = useState({
    title: "",
    description: "",
    patientCIN: "",
    additionalInfo: "",
    notes: "",
    date: Date.now(),
  });
  // set the rendred time to GMT+X WHERE X is the time offset of the user because the dat eis actualy GMT +0 for consistency of data
  const localDate = new Date(data.date);
  // localDate.setHours(localDate.getHours() - localDate.getTimezoneOffset() / 60);
  // Handles changes for text inputs
  const handleChange = (e) => {
    setData((prevData) => ({
      ...prevData,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(
      await addAppointementAPI({
        cin: data.patientCIN,
        date: data.date,
        description: data.description,
        info: data.additionalInfo,
        notes: data.notes,
        title: data.title,
        token: userData.token,
      })
    );
  };

  return (
    <div className="w-[76%] h-[76%] bg-white rounded-lg shadow-[0_4px_12px_rgba(0,0,0,0.1)] flex overflow-hidden m-auto text-black">
      <div className="w-1/2 p-5 border-r border-[#e0e0e0] box-border">
        <DateAppointementInput
          dateInput={localDate}
          onChange={(e) => {
            setData({ ...data, date: e.getTime() });
          }}
        />
        <TextField
          type="datetime-local"
          disabled
          className="w-full mt-2.5"
          value={localDate.toISOString().split(".")[0]}
        />
      </div>

      <div className="flex flex-col gap-4 w-1/2 p-5 box-border overflow-y-auto">
        <TextField
          label="title"
          name="title"
          placeholder="Title"
          variant="outlined"
          fullWidth
          value={data.title}
          onChange={handleChange}
        />
        <TextField
          label="description"
          name="description"
          placeholder="Description"
          variant="outlined"
          fullWidth
          multiline
          rows={3}
          value={data.description}
          onChange={handleChange}
        />
        <TextField
          label="patient CIN"
          name="patientCIN"
          placeholder="Patient CIN (optional)"
          variant="outlined"
          fullWidth
          value={data.patientCIN}
          onChange={handleChange}
        />
        <TextField
          label="additionalInfo"
          name="additionalInfo"
          placeholder="Additional Info"
          variant="outlined"
          fullWidth
          value={data.additionalInfo}
          onChange={handleChange}
        />
        <TextField
          label="notes"
          name="notes"
          placeholder="Notes"
          variant="outlined"
          fullWidth
          multiline
          rows={3}
          value={data.notes}
          onChange={handleChange}
        />
        <div className="mt-auto w-full">
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={handleSubmit}
          >
            Send
          </Button>
        </div>
      </div>
    </div>
  );
}
