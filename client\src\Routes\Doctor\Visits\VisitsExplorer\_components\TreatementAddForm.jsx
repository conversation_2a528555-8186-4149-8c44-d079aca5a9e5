import {
  Box,
  Button,
  TextField,
  Typography,
  MenuItem,
  Select,
} from "@mui/material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useState } from "react";
import { createTreatementAPI } from "../../../../../api";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../../data";
import toast from "react-hot-toast";

export default function TreatmentAddForm({}) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    start: new Date().getTime(),
    end: null,
    state: "O", // Default to Ongoing
  });
  const [patients, setPatients] = PatientsDataStore.useStore();
  const [CurrentSelectionState, setCurrentSelectionState] =
    visitsExplorerStore.useStore({});
  const userData = userDataStore.useStore({ setter: false });
  const handleSubmit = async (e) => {
    LoadingBarStore.setCurrent({ loading: true });
    e.preventDefault();
    formData.patient = patients.patient[CurrentSelectionState.patient]._id;
    formData.folder =
      patients.patient[CurrentSelectionState.patient].folder._id;
    const [err, data] = await createTreatementAPI(formData, userData.token);
    if (err) {
      toast.error("error adding treatement");
      console.log(err, formData);
      LoadingBarStore.setCurrent({ loading: false });
      return;
    }
    toast.success("treatement added succesfully");
    PopUpElement.setCurrent({ element: () => null });
    const newLength = patients.patient[
      CurrentSelectionState.patient
    ].folder.treatments.push(data.data);
    setPatients({ ...patients });
    setCurrentSelectionState({
      treatement: newLength - 1,
      visit: undefined,
    });
    console.log(data);
    LoadingBarStore.setCurrent({ loading: false });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="mb-2">
        New Treatment
      </Typography>

      <TextField
        label="Title"
        fullWidth
        required
        value={formData.title}
        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
        variant="outlined"
      />

      <TextField
        label="Description"
        fullWidth
        multiline
        rows={3}
        value={formData.description}
        onChange={(e) =>
          setFormData({ ...formData, description: e.target.value })
        }
        variant="outlined"
      />

      <Box className="flex gap-4 flex-wrap">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Start Date"
            value={new Date(formData.start)}
            onChange={(newDate) => {
              setFormData({
                ...formData,
                start: newDate.getTime(),
              });
            }}
            sx={{ flex: 1 }}
          />
          <DatePicker
            label="End Date (Optional)"
            value={formData.end ? new Date(formData.end) : null}
            onChange={(newDate) => {
              setFormData({
                ...formData,
                end: newDate ? newDate.getTime() : null,
              });
            }}
            sx={{ flex: 1 }}
          />
        </LocalizationProvider>
      </Box>

      <Select
        value={formData.state}
        label="Status"
        onChange={(e) => setFormData({ ...formData, state: e.target.value })}
        fullWidth
      >
        <MenuItem value="O">Ongoing</MenuItem>
        <MenuItem value="C">Completed</MenuItem>
        <MenuItem value="T">Terminated</MenuItem>
      </Select>

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Save Treatment
        </Button>
      </div>
    </Box>
  );
}
