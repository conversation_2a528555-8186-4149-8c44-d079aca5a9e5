import { useEffect, useState } from "react";
import { LoadingBarStore, PopUpElement, userDataStore } from "../../../../data";
import { getAppointementForDateAPI } from "../../../../api";
import AppointmentItem from "../_components/AppointementItem";
import AddAppointement from "../_components/AddAppointement";
import CalenderApointement from "../../../../utils/CalendarApointement";
import ShowPaitentAppointement from "../_components/ShowPaitentAppointement";

export default function CalanderSection() {
  //init vars
  const [selectedDate, setSelectedDate] = useState(new Date());
  const userData = userDataStore.useStore({ setter: false });
  const [appointements, setAppointements] = useState([]);
  const setLoadingScreen = LoadingBarStore.useStore({ getter: false });
  const setPopUpPage = PopUpElement.useStore({ getter: false });
  //*
  async function fetchAppointements() {
    setLoadingScreen({ loading: true });
    const startFetch = new Date(selectedDate);
    const endFetch = new Date(selectedDate);
    startFetch.setHours(0, 0, 0, 0);
    endFetch.setHours(23, 59, 59, 999);
    const result = await getAppointementForDateAPI({
      token: userData.token,
      start: startFetch.getTime(),
      end: endFetch.getTime(),
    });
    setLoadingScreen({ loading: false });
    if (result[0]) return console.log(result[0]);
    setAppointements(
      result[1].data.map((e) => {
        const date = new Date(e.date);
        return {
          ...e,
          time: date.toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
          }),
          title: e.title,
          description: e.description,
        };
      })
    );
  }

  //use effect for featching appointements
  useEffect(() => {
    fetchAppointements();
  }, [selectedDate]);

  return (
    <div className="w-[48%] bg-white min-h-full flex flex-col items-center p-2.5">
      <div className="calander w-full m-0">
        <CalenderApointement
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
        />
      </div>
      <div className="appointement w-full overflow-x-hidden max-h-[31vh]">
        <ul className="w-full flex flex-col h-full overflow-y-auto px-2.5 overflow-x-hidden">
          {appointements.map((e) => (
            <AppointmentItem
              description={e.description}
              time={e.time}
              title={e.title}
              LiProps={{
                onClick: () => {
                  setPopUpPage({
                    element: () => <ShowPaitentAppointement appointement={e} />,
                  });
                },
              }}
            />
          ))}
        </ul>
      </div>
      <AddAppointement
        props={{
          onClick: () => {
            console.log(selectedDate);
          },
        }}
      />
    </div>
  );
}
