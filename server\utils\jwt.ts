import { JwtUserAuthPayload } from "../types/jwt";
import jwt from "jsonwebtoken";
export function generateUserJWT(data: JwtUserAuthPayload) {
  return jwt.sign(data, process.env.JWT_SECRET as string, {
    expiresIn: "1 day",
  });
}
export function validateUserJWT(encoded: string): JwtUserAuthPayload {
  return jwt.verify(
    encoded,
    process.env.JWT_SECRET as string
  ) as JwtUserAuthPayload;
}
