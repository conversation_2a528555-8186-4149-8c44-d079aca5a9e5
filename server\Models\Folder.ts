import { Schema, model, Types } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IFolder extends BaseModel {
  patient: Types.ObjectId;
  treatments: Types.ObjectId[];
  start: number;
  notes: string;
}

const folderSchema = new Schema<IFolder>({
  patient: {
    type: Schema.Types.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  treatments: [
    { type: Schema.Types.ObjectId, ref: ModelRefs.Treatment.modelName },
  ],
  start: { type: Number, required: true },
  notes: { type: String, default: "" },
  createdAt: Number,
  updatedAt: Number,
});

export const Folder = model<IFolder>(ModelRefs.Folder.modelName, folderSchema);
