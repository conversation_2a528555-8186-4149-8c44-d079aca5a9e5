@import "tailwindcss";

@font-face {
  font-family: "Roboto";
  src: url("/Roboto/Roboto-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "Roboto";
  src: url("/Roboto/Roboto-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}
@font-face {
  font-family: "Roboto";
  src: url("/Roboto/Roboto-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}
@font-face {
  font-family: "Roboto";
  src: url("/Roboto/Roboto-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Roboto";
  src: url("/Roboto/Roboto-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

@layer base {
  * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    list-style: none;
    transition: all 0.1s ease-in-out;
    font-family: "Roboto", sans-serif;
    outline: none;
  }
  i {
    cursor: pointer;
  }
  :root {
    --bg: #f8fafc; /* main background */
    --surface: #ffffff; /* cards, panels */
    --primary: #2563eb; /* blue-600 */
    --primary-hover: #1e40af; /* darker blue for hover */
    --secondary-color: #004292; /* slate-500 */
    --secondary-hover: #003370; /* darker slate for hover */
    --text: rgb(31, 35, 40); /* primary text */
    --text-muted: #4b5563; /* secondary text */
    --border: #d1d5db; /* borders and dividers */
    --shadow: rgba(0, 0, 0, 0.1); /* light shadow */
  }
}

/* Light mode palette */
@theme {
  --color-bg: #f8fafc; /* main background */
  --color-surface: #ffffff; /* cards, panels */
  --color-primary: #2563eb; /* blue-600 */
  --color-primary-hover: #1e40af; /* darker blue for hover */
  --color-secondary: #004292; /* slate-500 */
  --color-secondary-hover: #003370; /* darker slate for hover */
  --color-text: rgb(31, 35, 40); /* primary text */
  --color-text-muted: #4b5563; /* secondary text */
  --color-border: #d1d5db; /* borders and dividers */
  --color-shadow: rgba(0, 0, 0, 0.1); /* light shadow */
}

/* Dark mode palette */
.dark {
  --color-bg: #18181b; /* main background */
  --color-surface: #222222; /* cards, panels */
  --color-primary: #7fb9ff; /* blue-400 */
  --color-primary-hover: #4a8cff; /* lighter blue for hover */
  --color-secondary: #00bbff; /* slate-800 */
  --color-secondary-hover: #008ecc; /* darker slate for hover */
  --color-text: #f8fafc; /* primary text */
  --color-text-muted: #9ca3af; /* secondary text */
  --color-border: #37373d; /* borders and dividers */
  --color-shadow: rgba(0, 0, 0, 0.7); /* stronger shadow */
}

*::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

*::-webkit-scrollbar-thumb:hover {
  background: #555;
}
