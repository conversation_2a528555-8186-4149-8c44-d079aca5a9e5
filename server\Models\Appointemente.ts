import { Schema, model, Types } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IAppointement extends BaseModel {
  title: string;
  description: string;
  patientCIN?: string;
  additionalInfo?: string;
  notes?: string;
  date: Number; // Unix timestamp
  patient?: Types.ObjectId; // Reference to the patient
  doctor: Types.ObjectId; // Reference to the doctor
}

const AppointementSchema = new Schema<IAppointement>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  patientCIN: { type: String },
  additionalInfo: { type: String },
  notes: { type: String },
  date: { type: Number, required: true },
  patient: { type: Schema.Types.ObjectId, ref: ModelRefs.User.modelName },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  createdAt: Number,
  updatedAt: Number,
});

export const Appointement = model<IAppointement>(
  ModelRefs.Appointement.modelName,
  AppointementSchema
);
